<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Attribute' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/Attribute.php',
    'Automattic\\Jetpack\\A8c_Mc_Stats' => $vendorDir . '/automattic/jetpack-a8c-mc-stats/src/class-a8c-mc-stats.php',
    'Automattic\\Jetpack\\Admin_UI\\Admin_Menu' => $vendorDir . '/automattic/jetpack-admin-ui/src/class-admin-menu.php',
    'Automattic\\Jetpack\\Assets' => $vendorDir . '/automattic/jetpack-assets/src/class-assets.php',
    'Automattic\\Jetpack\\Assets\\Script_Data' => $vendorDir . '/automattic/jetpack-assets/src/class-script-data.php',
    'Automattic\\Jetpack\\Assets\\Semver' => $vendorDir . '/automattic/jetpack-assets/src/class-semver.php',
    'Automattic\\Jetpack\\Autoloader\\AutoloadFileWriter' => $vendorDir . '/automattic/jetpack-autoloader/src/AutoloadFileWriter.php',
    'Automattic\\Jetpack\\Autoloader\\AutoloadGenerator' => $vendorDir . '/automattic/jetpack-autoloader/src/AutoloadGenerator.php',
    'Automattic\\Jetpack\\Autoloader\\AutoloadProcessor' => $vendorDir . '/automattic/jetpack-autoloader/src/AutoloadProcessor.php',
    'Automattic\\Jetpack\\Autoloader\\CustomAutoloaderPlugin' => $vendorDir . '/automattic/jetpack-autoloader/src/CustomAutoloaderPlugin.php',
    'Automattic\\Jetpack\\Autoloader\\ManifestGenerator' => $vendorDir . '/automattic/jetpack-autoloader/src/ManifestGenerator.php',
    'Automattic\\Jetpack\\Config' => $vendorDir . '/automattic/jetpack-config/src/class-config.php',
    'Automattic\\Jetpack\\Connection\\Authorize_Json_Api' => $vendorDir . '/automattic/jetpack-connection/src/class-authorize-json-api.php',
    'Automattic\\Jetpack\\Connection\\Client' => $vendorDir . '/automattic/jetpack-connection/src/class-client.php',
    'Automattic\\Jetpack\\Connection\\Connection_Assets' => $vendorDir . '/automattic/jetpack-connection/src/class-connection-assets.php',
    'Automattic\\Jetpack\\Connection\\Connection_Notice' => $vendorDir . '/automattic/jetpack-connection/src/class-connection-notice.php',
    'Automattic\\Jetpack\\Connection\\Error_Handler' => $vendorDir . '/automattic/jetpack-connection/src/class-error-handler.php',
    'Automattic\\Jetpack\\Connection\\Initial_State' => $vendorDir . '/automattic/jetpack-connection/src/class-initial-state.php',
    'Automattic\\Jetpack\\Connection\\Manager' => $vendorDir . '/automattic/jetpack-connection/src/class-manager.php',
    'Automattic\\Jetpack\\Connection\\Manager_Interface' => $vendorDir . '/automattic/jetpack-connection/src/interface-manager.php',
    'Automattic\\Jetpack\\Connection\\Nonce_Handler' => $vendorDir . '/automattic/jetpack-connection/src/class-nonce-handler.php',
    'Automattic\\Jetpack\\Connection\\Package_Version' => $vendorDir . '/automattic/jetpack-connection/src/class-package-version.php',
    'Automattic\\Jetpack\\Connection\\Package_Version_Tracker' => $vendorDir . '/automattic/jetpack-connection/src/class-package-version-tracker.php',
    'Automattic\\Jetpack\\Connection\\Plugin' => $vendorDir . '/automattic/jetpack-connection/src/class-plugin.php',
    'Automattic\\Jetpack\\Connection\\Plugin_Storage' => $vendorDir . '/automattic/jetpack-connection/src/class-plugin-storage.php',
    'Automattic\\Jetpack\\Connection\\REST_Connector' => $vendorDir . '/automattic/jetpack-connection/src/class-rest-connector.php',
    'Automattic\\Jetpack\\Connection\\Rest_Authentication' => $vendorDir . '/automattic/jetpack-connection/src/class-rest-authentication.php',
    'Automattic\\Jetpack\\Connection\\SSO' => $vendorDir . '/automattic/jetpack-connection/src/sso/class-sso.php',
    'Automattic\\Jetpack\\Connection\\SSO\\Force_2FA' => $vendorDir . '/automattic/jetpack-connection/src/sso/class-force-2fa.php',
    'Automattic\\Jetpack\\Connection\\SSO\\Helpers' => $vendorDir . '/automattic/jetpack-connection/src/sso/class-helpers.php',
    'Automattic\\Jetpack\\Connection\\SSO\\Notices' => $vendorDir . '/automattic/jetpack-connection/src/sso/class-notices.php',
    'Automattic\\Jetpack\\Connection\\SSO\\User_Admin' => $vendorDir . '/automattic/jetpack-connection/src/sso/class-user-admin.php',
    'Automattic\\Jetpack\\Connection\\Secrets' => $vendorDir . '/automattic/jetpack-connection/src/class-secrets.php',
    'Automattic\\Jetpack\\Connection\\Server_Sandbox' => $vendorDir . '/automattic/jetpack-connection/src/class-server-sandbox.php',
    'Automattic\\Jetpack\\Connection\\Tokens' => $vendorDir . '/automattic/jetpack-connection/src/class-tokens.php',
    'Automattic\\Jetpack\\Connection\\Tokens_Locks' => $vendorDir . '/automattic/jetpack-connection/src/class-tokens-locks.php',
    'Automattic\\Jetpack\\Connection\\Urls' => $vendorDir . '/automattic/jetpack-connection/src/class-urls.php',
    'Automattic\\Jetpack\\Connection\\Utils' => $vendorDir . '/automattic/jetpack-connection/src/class-utils.php',
    'Automattic\\Jetpack\\Connection\\Webhooks' => $vendorDir . '/automattic/jetpack-connection/src/class-webhooks.php',
    'Automattic\\Jetpack\\Connection\\Webhooks\\Authorize_Redirect' => $vendorDir . '/automattic/jetpack-connection/src/webhooks/class-authorize-redirect.php',
    'Automattic\\Jetpack\\Connection\\XMLRPC_Async_Call' => $vendorDir . '/automattic/jetpack-connection/src/class-xmlrpc-async-call.php',
    'Automattic\\Jetpack\\Connection\\XMLRPC_Connector' => $vendorDir . '/automattic/jetpack-connection/src/class-xmlrpc-connector.php',
    'Automattic\\Jetpack\\Constants' => $vendorDir . '/automattic/jetpack-constants/src/class-constants.php',
    'Automattic\\Jetpack\\CookieState' => $vendorDir . '/automattic/jetpack-status/src/class-cookiestate.php',
    'Automattic\\Jetpack\\Errors' => $vendorDir . '/automattic/jetpack-status/src/class-errors.php',
    'Automattic\\Jetpack\\Files' => $vendorDir . '/automattic/jetpack-status/src/class-files.php',
    'Automattic\\Jetpack\\Heartbeat' => $vendorDir . '/automattic/jetpack-connection/src/class-heartbeat.php',
    'Automattic\\Jetpack\\IdentityCrisis\\Exception' => $vendorDir . '/automattic/jetpack-connection/src/identity-crisis/class-exception.php',
    'Automattic\\Jetpack\\IdentityCrisis\\REST_Endpoints' => $vendorDir . '/automattic/jetpack-connection/src/identity-crisis/class-rest-endpoints.php',
    'Automattic\\Jetpack\\IdentityCrisis\\UI' => $vendorDir . '/automattic/jetpack-connection/src/identity-crisis/class-ui.php',
    'Automattic\\Jetpack\\IdentityCrisis\\URL_Secret' => $vendorDir . '/automattic/jetpack-connection/src/identity-crisis/class-url-secret.php',
    'Automattic\\Jetpack\\Identity_Crisis' => $vendorDir . '/automattic/jetpack-connection/src/identity-crisis/class-identity-crisis.php',
    'Automattic\\Jetpack\\Modules' => $vendorDir . '/automattic/jetpack-status/src/class-modules.php',
    'Automattic\\Jetpack\\Partner' => $vendorDir . '/automattic/jetpack-connection/src/class-partner.php',
    'Automattic\\Jetpack\\Partner_Coupon' => $vendorDir . '/automattic/jetpack-connection/src/class-partner-coupon.php',
    'Automattic\\Jetpack\\Paths' => $vendorDir . '/automattic/jetpack-status/src/class-paths.php',
    'Automattic\\Jetpack\\Redirect' => $vendorDir . '/automattic/jetpack-redirect/src/class-redirect.php',
    'Automattic\\Jetpack\\Roles' => $vendorDir . '/automattic/jetpack-roles/src/class-roles.php',
    'Automattic\\Jetpack\\Status' => $vendorDir . '/automattic/jetpack-status/src/class-status.php',
    'Automattic\\Jetpack\\Status\\Cache' => $vendorDir . '/automattic/jetpack-status/src/class-cache.php',
    'Automattic\\Jetpack\\Status\\Host' => $vendorDir . '/automattic/jetpack-status/src/class-host.php',
    'Automattic\\Jetpack\\Status\\Visitor' => $vendorDir . '/automattic/jetpack-status/src/class-visitor.php',
    'Automattic\\Jetpack\\Terms_Of_Service' => $vendorDir . '/automattic/jetpack-connection/src/class-terms-of-service.php',
    'Automattic\\Jetpack\\Tracking' => $vendorDir . '/automattic/jetpack-connection/src/class-tracking.php',
    'Automattic\\WooCommerce\\Admin\\API\\AI\\BusinessDescription' => $baseDir . '/src/Admin/API/AI/BusinessDescription.php',
    'Automattic\\WooCommerce\\Admin\\API\\AI\\Images' => $baseDir . '/src/Admin/API/AI/Images.php',
    'Automattic\\WooCommerce\\Admin\\API\\AI\\Middleware' => $baseDir . '/src/Admin/API/AI/Middleware.php',
    'Automattic\\WooCommerce\\Admin\\API\\AI\\Patterns' => $baseDir . '/src/Admin/API/AI/Patterns.php',
    'Automattic\\WooCommerce\\Admin\\API\\AI\\Product' => $baseDir . '/src/Admin/API/AI/Product.php',
    'Automattic\\WooCommerce\\Admin\\API\\AI\\StoreInfo' => $baseDir . '/src/Admin/API/AI/StoreInfo.php',
    'Automattic\\WooCommerce\\Admin\\API\\AI\\StoreTitle' => $baseDir . '/src/Admin/API/AI/StoreTitle.php',
    'Automattic\\WooCommerce\\Admin\\API\\Coupons' => $baseDir . '/src/Admin/API/Coupons.php',
    'Automattic\\WooCommerce\\Admin\\API\\CustomAttributeTraits' => $baseDir . '/src/Admin/API/CustomAttributeTraits.php',
    'Automattic\\WooCommerce\\Admin\\API\\Customers' => $baseDir . '/src/Admin/API/Customers.php',
    'Automattic\\WooCommerce\\Admin\\API\\Data' => $baseDir . '/src/Admin/API/Data.php',
    'Automattic\\WooCommerce\\Admin\\API\\DataCountries' => $baseDir . '/src/Admin/API/DataCountries.php',
    'Automattic\\WooCommerce\\Admin\\API\\DataDownloadIPs' => $baseDir . '/src/Admin/API/DataDownloadIPs.php',
    'Automattic\\WooCommerce\\Admin\\API\\Experiments' => $baseDir . '/src/Admin/API/Experiments.php',
    'Automattic\\WooCommerce\\Admin\\API\\Features' => $baseDir . '/src/Admin/API/Features.php',
    'Automattic\\WooCommerce\\Admin\\API\\Init' => $baseDir . '/src/Admin/API/Init.php',
    'Automattic\\WooCommerce\\Admin\\API\\LaunchYourStore' => $baseDir . '/src/Admin/API/LaunchYourStore.php',
    'Automattic\\WooCommerce\\Admin\\API\\Leaderboards' => $baseDir . '/src/Admin/API/Leaderboards.php',
    'Automattic\\WooCommerce\\Admin\\API\\Marketing' => $baseDir . '/src/Admin/API/Marketing.php',
    'Automattic\\WooCommerce\\Admin\\API\\MarketingCampaignTypes' => $baseDir . '/src/Admin/API/MarketingCampaignTypes.php',
    'Automattic\\WooCommerce\\Admin\\API\\MarketingCampaigns' => $baseDir . '/src/Admin/API/MarketingCampaigns.php',
    'Automattic\\WooCommerce\\Admin\\API\\MarketingChannels' => $baseDir . '/src/Admin/API/MarketingChannels.php',
    'Automattic\\WooCommerce\\Admin\\API\\MarketingOverview' => $baseDir . '/src/Admin/API/MarketingOverview.php',
    'Automattic\\WooCommerce\\Admin\\API\\MarketingRecommendations' => $baseDir . '/src/Admin/API/MarketingRecommendations.php',
    'Automattic\\WooCommerce\\Admin\\API\\MobileAppMagicLink' => $baseDir . '/src/Admin/API/MobileAppMagicLink.php',
    'Automattic\\WooCommerce\\Admin\\API\\NoteActions' => $baseDir . '/src/Admin/API/NoteActions.php',
    'Automattic\\WooCommerce\\Admin\\API\\Notes' => $baseDir . '/src/Admin/API/Notes.php',
    'Automattic\\WooCommerce\\Admin\\API\\Notice' => $baseDir . '/src/Admin/API/Notice.php',
    'Automattic\\WooCommerce\\Admin\\API\\OnboardingFreeExtensions' => $baseDir . '/src/Admin/API/OnboardingFreeExtensions.php',
    'Automattic\\WooCommerce\\Admin\\API\\OnboardingPlugins' => $baseDir . '/src/Admin/API/OnboardingPlugins.php',
    'Automattic\\WooCommerce\\Admin\\API\\OnboardingProductTypes' => $baseDir . '/src/Admin/API/OnboardingProductTypes.php',
    'Automattic\\WooCommerce\\Admin\\API\\OnboardingProducts' => $baseDir . '/src/Admin/API/OnboardingProducts.php',
    'Automattic\\WooCommerce\\Admin\\API\\OnboardingProfile' => $baseDir . '/src/Admin/API/OnboardingProfile.php',
    'Automattic\\WooCommerce\\Admin\\API\\OnboardingTasks' => $baseDir . '/src/Admin/API/OnboardingTasks.php',
    'Automattic\\WooCommerce\\Admin\\API\\OnboardingThemes' => $baseDir . '/src/Admin/API/OnboardingThemes.php',
    'Automattic\\WooCommerce\\Admin\\API\\Options' => $baseDir . '/src/Admin/API/Options.php',
    'Automattic\\WooCommerce\\Admin\\API\\Orders' => $baseDir . '/src/Admin/API/Orders.php',
    'Automattic\\WooCommerce\\Admin\\API\\PaymentGatewaySuggestions' => $baseDir . '/src/Admin/API/PaymentGatewaySuggestions.php',
    'Automattic\\WooCommerce\\Admin\\API\\Plugins' => $baseDir . '/src/Admin/API/Plugins.php',
    'Automattic\\WooCommerce\\Admin\\API\\ProductAttributeTerms' => $baseDir . '/src/Admin/API/ProductAttributeTerms.php',
    'Automattic\\WooCommerce\\Admin\\API\\ProductAttributes' => $baseDir . '/src/Admin/API/ProductAttributes.php',
    'Automattic\\WooCommerce\\Admin\\API\\ProductCategories' => $baseDir . '/src/Admin/API/ProductCategories.php',
    'Automattic\\WooCommerce\\Admin\\API\\ProductForm' => $baseDir . '/src/Admin/API/ProductForm.php',
    'Automattic\\WooCommerce\\Admin\\API\\ProductReviews' => $baseDir . '/src/Admin/API/ProductReviews.php',
    'Automattic\\WooCommerce\\Admin\\API\\ProductVariations' => $baseDir . '/src/Admin/API/ProductVariations.php',
    'Automattic\\WooCommerce\\Admin\\API\\Products' => $baseDir . '/src/Admin/API/Products.php',
    'Automattic\\WooCommerce\\Admin\\API\\ProductsLowInStock' => $baseDir . '/src/Admin/API/ProductsLowInStock.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Cache' => $baseDir . '/src/Admin/API/Reports/Cache.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Categories\\Controller' => $baseDir . '/src/Admin/API/Reports/Categories/Controller.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Categories\\DataStore' => $baseDir . '/src/Admin/API/Reports/Categories/DataStore.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Categories\\Query' => $baseDir . '/src/Admin/API/Reports/Categories/Query.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Controller' => $baseDir . '/src/Admin/API/Reports/Controller.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Coupons\\Controller' => $baseDir . '/src/Admin/API/Reports/Coupons/Controller.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Coupons\\DataStore' => $baseDir . '/src/Admin/API/Reports/Coupons/DataStore.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Coupons\\Query' => $baseDir . '/src/Admin/API/Reports/Coupons/Query.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Coupons\\Stats\\Controller' => $baseDir . '/src/Admin/API/Reports/Coupons/Stats/Controller.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Coupons\\Stats\\DataStore' => $baseDir . '/src/Admin/API/Reports/Coupons/Stats/DataStore.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Coupons\\Stats\\Query' => $baseDir . '/src/Admin/API/Reports/Coupons/Stats/Query.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Coupons\\Stats\\Segmenter' => $baseDir . '/src/Admin/API/Reports/Coupons/Stats/Segmenter.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Customers\\Controller' => $baseDir . '/src/Admin/API/Reports/Customers/Controller.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Customers\\DataStore' => $baseDir . '/src/Admin/API/Reports/Customers/DataStore.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Customers\\Query' => $baseDir . '/src/Admin/API/Reports/Customers/Query.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Customers\\Stats\\Controller' => $baseDir . '/src/Admin/API/Reports/Customers/Stats/Controller.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Customers\\Stats\\DataStore' => $baseDir . '/src/Admin/API/Reports/Customers/Stats/DataStore.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Customers\\Stats\\Query' => $baseDir . '/src/Admin/API/Reports/Customers/Stats/Query.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\DataStore' => $baseDir . '/src/Admin/API/Reports/DataStore.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\DataStoreInterface' => $baseDir . '/src/Admin/API/Reports/DataStoreInterface.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Downloads\\Controller' => $baseDir . '/src/Admin/API/Reports/Downloads/Controller.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Downloads\\DataStore' => $baseDir . '/src/Admin/API/Reports/Downloads/DataStore.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Downloads\\Files\\Controller' => $baseDir . '/src/Admin/API/Reports/Downloads/Files/Controller.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Downloads\\Query' => $baseDir . '/src/Admin/API/Reports/Downloads/Query.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Downloads\\Stats\\Controller' => $baseDir . '/src/Admin/API/Reports/Downloads/Stats/Controller.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Downloads\\Stats\\DataStore' => $baseDir . '/src/Admin/API/Reports/Downloads/Stats/DataStore.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Downloads\\Stats\\Query' => $baseDir . '/src/Admin/API/Reports/Downloads/Stats/Query.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Export\\Controller' => $baseDir . '/src/Admin/API/Reports/Export/Controller.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\ExportableInterface' => $baseDir . '/src/Admin/API/Reports/ExportableInterface.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\ExportableTraits' => $baseDir . '/src/Admin/API/Reports/ExportableTraits.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\FilteredGetDataTrait' => $baseDir . '/src/Admin/API/Reports/FilteredGetDataTrait.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\GenericController' => $baseDir . '/src/Admin/API/Reports/GenericController.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\GenericQuery' => $baseDir . '/src/Admin/API/Reports/GenericQuery.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\GenericStatsController' => $baseDir . '/src/Admin/API/Reports/GenericStatsController.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Import\\Controller' => $baseDir . '/src/Admin/API/Reports/Import/Controller.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\OrderAwareControllerTrait' => $baseDir . '/src/Admin/API/Reports/OrderAwareControllerTrait.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Orders\\Controller' => $baseDir . '/src/Admin/API/Reports/Orders/Controller.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Orders\\DataStore' => $baseDir . '/src/Admin/API/Reports/Orders/DataStore.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Orders\\Query' => $baseDir . '/src/Admin/API/Reports/Orders/Query.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Orders\\Stats\\Controller' => $baseDir . '/src/Admin/API/Reports/Orders/Stats/Controller.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Orders\\Stats\\DataStore' => $baseDir . '/src/Admin/API/Reports/Orders/Stats/DataStore.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Orders\\Stats\\Query' => $baseDir . '/src/Admin/API/Reports/Orders/Stats/Query.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Orders\\Stats\\Segmenter' => $baseDir . '/src/Admin/API/Reports/Orders/Stats/Segmenter.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\ParameterException' => $baseDir . '/src/Admin/API/Reports/ParameterException.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\PerformanceIndicators\\Controller' => $baseDir . '/src/Admin/API/Reports/PerformanceIndicators/Controller.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Products\\Controller' => $baseDir . '/src/Admin/API/Reports/Products/Controller.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Products\\DataStore' => $baseDir . '/src/Admin/API/Reports/Products/DataStore.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Products\\Query' => $baseDir . '/src/Admin/API/Reports/Products/Query.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Products\\Stats\\Controller' => $baseDir . '/src/Admin/API/Reports/Products/Stats/Controller.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Products\\Stats\\DataStore' => $baseDir . '/src/Admin/API/Reports/Products/Stats/DataStore.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Products\\Stats\\Query' => $baseDir . '/src/Admin/API/Reports/Products/Stats/Query.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Products\\Stats\\Segmenter' => $baseDir . '/src/Admin/API/Reports/Products/Stats/Segmenter.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Query' => $baseDir . '/src/Admin/API/Reports/Query.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Revenue\\Query' => $baseDir . '/src/Admin/API/Reports/Revenue/Query.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Revenue\\Stats\\Controller' => $baseDir . '/src/Admin/API/Reports/Revenue/Stats/Controller.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Segmenter' => $baseDir . '/src/Admin/API/Reports/Segmenter.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\SqlQuery' => $baseDir . '/src/Admin/API/Reports/SqlQuery.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\StatsDataStoreTrait' => $baseDir . '/src/Admin/API/Reports/StatsDataStoreTrait.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Stock\\Controller' => $baseDir . '/src/Admin/API/Reports/Stock/Controller.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Stock\\Stats\\Controller' => $baseDir . '/src/Admin/API/Reports/Stock/Stats/Controller.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Stock\\Stats\\DataStore' => $baseDir . '/src/Admin/API/Reports/Stock/Stats/DataStore.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Stock\\Stats\\Query' => $baseDir . '/src/Admin/API/Reports/Stock/Stats/Query.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Taxes\\Controller' => $baseDir . '/src/Admin/API/Reports/Taxes/Controller.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Taxes\\DataStore' => $baseDir . '/src/Admin/API/Reports/Taxes/DataStore.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Taxes\\Query' => $baseDir . '/src/Admin/API/Reports/Taxes/Query.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Taxes\\Stats\\Controller' => $baseDir . '/src/Admin/API/Reports/Taxes/Stats/Controller.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Taxes\\Stats\\DataStore' => $baseDir . '/src/Admin/API/Reports/Taxes/Stats/DataStore.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Taxes\\Stats\\Query' => $baseDir . '/src/Admin/API/Reports/Taxes/Stats/Query.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Taxes\\Stats\\Segmenter' => $baseDir . '/src/Admin/API/Reports/Taxes/Stats/Segmenter.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\TimeInterval' => $baseDir . '/src/Admin/API/Reports/TimeInterval.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Variations\\Controller' => $baseDir . '/src/Admin/API/Reports/Variations/Controller.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Variations\\DataStore' => $baseDir . '/src/Admin/API/Reports/Variations/DataStore.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Variations\\Query' => $baseDir . '/src/Admin/API/Reports/Variations/Query.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Variations\\Stats\\Controller' => $baseDir . '/src/Admin/API/Reports/Variations/Stats/Controller.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Variations\\Stats\\DataStore' => $baseDir . '/src/Admin/API/Reports/Variations/Stats/DataStore.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Variations\\Stats\\Query' => $baseDir . '/src/Admin/API/Reports/Variations/Stats/Query.php',
    'Automattic\\WooCommerce\\Admin\\API\\Reports\\Variations\\Stats\\Segmenter' => $baseDir . '/src/Admin/API/Reports/Variations/Stats/Segmenter.php',
    'Automattic\\WooCommerce\\Admin\\API\\SettingOptions' => $baseDir . '/src/Admin/API/SettingOptions.php',
    'Automattic\\WooCommerce\\Admin\\API\\Settings' => $baseDir . '/src/Admin/API/Settings.php',
    'Automattic\\WooCommerce\\Admin\\API\\ShippingPartnerSuggestions' => $baseDir . '/src/Admin/API/ShippingPartnerSuggestions.php',
    'Automattic\\WooCommerce\\Admin\\API\\Taxes' => $baseDir . '/src/Admin/API/Taxes.php',
    'Automattic\\WooCommerce\\Admin\\API\\Themes' => $baseDir . '/src/Admin/API/Themes.php',
    'Automattic\\WooCommerce\\Admin\\BlockTemplates\\BlockContainerInterface' => $baseDir . '/src/Admin/BlockTemplates/BlockContainerInterface.php',
    'Automattic\\WooCommerce\\Admin\\BlockTemplates\\BlockInterface' => $baseDir . '/src/Admin/BlockTemplates/BlockInterface.php',
    'Automattic\\WooCommerce\\Admin\\BlockTemplates\\BlockTemplateInterface' => $baseDir . '/src/Admin/BlockTemplates/BlockTemplateInterface.php',
    'Automattic\\WooCommerce\\Admin\\BlockTemplates\\ContainerInterface' => $baseDir . '/src/Admin/BlockTemplates/ContainerInterface.php',
    'Automattic\\WooCommerce\\Admin\\Composer\\Package' => $baseDir . '/src/Admin/Composer/Package.php',
    'Automattic\\WooCommerce\\Admin\\DataSourcePoller' => $baseDir . '/src/Admin/DataSourcePoller.php',
    'Automattic\\WooCommerce\\Admin\\DateTimeProvider\\CurrentDateTimeProvider' => $baseDir . '/src/Admin/DateTimeProvider/CurrentDateTimeProvider.php',
    'Automattic\\WooCommerce\\Admin\\DateTimeProvider\\DateTimeProviderInterface' => $baseDir . '/src/Admin/DateTimeProvider/DateTimeProviderInterface.php',
    'Automattic\\WooCommerce\\Admin\\DeprecatedClassFacade' => $baseDir . '/src/Admin/DeprecatedClassFacade.php',
    'Automattic\\WooCommerce\\Admin\\FeaturePlugin' => $baseDir . '/src/Admin/FeaturePlugin.php',
    'Automattic\\WooCommerce\\Admin\\Features\\AsyncProductEditorCategoryField\\Init' => $baseDir . '/src/Admin/Features/AsyncProductEditorCategoryField/Init.php',
    'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\Exporters\\ExportWCCoreProfilerOptions' => $baseDir . '/src/Admin/Features/Blueprint/Exporters/ExportWCCoreProfilerOptions.php',
    'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\Exporters\\ExportWCPaymentGateways' => $baseDir . '/src/Admin/Features/Blueprint/Exporters/ExportWCPaymentGateways.php',
    'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\Exporters\\ExportWCSettings' => $baseDir . '/src/Admin/Features/Blueprint/Exporters/ExportWCSettings.php',
    'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\Exporters\\ExportWCSettingsAccount' => $baseDir . '/src/Admin/Features/Blueprint/Exporters/ExportWCSettingsAccount.php',
    'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\Exporters\\ExportWCSettingsAdvanced' => $baseDir . '/src/Admin/Features/Blueprint/Exporters/ExportWCSettingsAdvanced.php',
    'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\Exporters\\ExportWCSettingsEmails' => $baseDir . '/src/Admin/Features/Blueprint/Exporters/ExportWCSettingsEmails.php',
    'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\Exporters\\ExportWCSettingsGeneral' => $baseDir . '/src/Admin/Features/Blueprint/Exporters/ExportWCSettingsGeneral.php',
    'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\Exporters\\ExportWCSettingsIntegrations' => $baseDir . '/src/Admin/Features/Blueprint/Exporters/ExportWCSettingsIntegrations.php',
    'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\Exporters\\ExportWCSettingsProducts' => $baseDir . '/src/Admin/Features/Blueprint/Exporters/ExportWCSettingsProducts.php',
    'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\Exporters\\ExportWCSettingsShipping' => $baseDir . '/src/Admin/Features/Blueprint/Exporters/ExportWCSettingsShipping.php',
    'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\Exporters\\ExportWCSettingsSiteVisibility' => $baseDir . '/src/Admin/Features/Blueprint/Exporters/ExportWCSettingsSiteVisibility.php',
    'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\Exporters\\ExportWCSettingsTax' => $baseDir . '/src/Admin/Features/Blueprint/Exporters/ExportWCSettingsTax.php',
    'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\Exporters\\ExportWCTaskOptions' => $baseDir . '/src/Admin/Features/Blueprint/Exporters/ExportWCTaskOptions.php',
    'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\Init' => $baseDir . '/src/Admin/Features/Blueprint/Init.php',
    'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\RestApi' => $baseDir . '/src/Admin/Features/Blueprint/RestApi.php',
    'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\SettingOptions' => $baseDir . '/src/Admin/Features/Blueprint/SettingOptions.php',
    'Automattic\\WooCommerce\\Admin\\Features\\Features' => $baseDir . '/src/Admin/Features/Features.php',
    'Automattic\\WooCommerce\\Admin\\Features\\LaunchYourStore' => $baseDir . '/src/Admin/Features/LaunchYourStore.php',
    'Automattic\\WooCommerce\\Admin\\Features\\MarketingRecommendations\\DefaultMarketingRecommendations' => $baseDir . '/src/Admin/Features/MarketingRecommendations/DefaultMarketingRecommendations.php',
    'Automattic\\WooCommerce\\Admin\\Features\\MarketingRecommendations\\Init' => $baseDir . '/src/Admin/Features/MarketingRecommendations/Init.php',
    'Automattic\\WooCommerce\\Admin\\Features\\MarketingRecommendations\\MarketingRecommendationsDataSourcePoller' => $baseDir . '/src/Admin/Features/MarketingRecommendations/MarketingRecommendationsDataSourcePoller.php',
    'Automattic\\WooCommerce\\Admin\\Features\\MarketingRecommendations\\MiscRecommendationsDataSourcePoller' => $baseDir . '/src/Admin/Features/MarketingRecommendations/MiscRecommendationsDataSourcePoller.php',
    'Automattic\\WooCommerce\\Admin\\Features\\Navigation\\RemovedDeprecated' => $baseDir . '/src/Admin/Features/Navigation/RemovedDeprecated.php',
    'Automattic\\WooCommerce\\Admin\\Features\\Onboarding' => $baseDir . '/src/Admin/Features/Onboarding.php',
    'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\DeprecatedExtendedTask' => $baseDir . '/src/Admin/Features/OnboardingTasks/DeprecatedExtendedTask.php',
    'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\DeprecatedOptions' => $baseDir . '/src/Admin/Features/OnboardingTasks/DeprecatedOptions.php',
    'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Init' => $baseDir . '/src/Admin/Features/OnboardingTasks/Init.php',
    'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Task' => $baseDir . '/src/Admin/Features/OnboardingTasks/Task.php',
    'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\TaskList' => $baseDir . '/src/Admin/Features/OnboardingTasks/TaskList.php',
    'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\TaskListSection' => $baseDir . '/src/Admin/Features/OnboardingTasks/TaskListSection.php',
    'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\TaskLists' => $baseDir . '/src/Admin/Features/OnboardingTasks/TaskLists.php',
    'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\TaskTraits' => $baseDir . '/src/Admin/Features/OnboardingTasks/TaskTraits.php',
    'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\AdditionalPayments' => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/AdditionalPayments.php',
    'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\Appearance' => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/Appearance.php',
    'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\CustomizeStore' => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/CustomizeStore.php',
    'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\ExperimentalShippingRecommendation' => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/ExperimentalShippingRecommendation.php',
    'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\ExtendStore' => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/ExtendStore.php',
    'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\GetMobileApp' => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/GetMobileApp.php',
    'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\LaunchYourStore' => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/LaunchYourStore.php',
    'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\Marketing' => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/Marketing.php',
    'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\Payments' => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/Payments.php',
    'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\Products' => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/Products.php',
    'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\ReviewShippingOptions' => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/ReviewShippingOptions.php',
    'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\Shipping' => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/Shipping.php',
    'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\StoreCreation' => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/StoreCreation.php',
    'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\StoreDetails' => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/StoreDetails.php',
    'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\Tax' => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/Tax.php',
    'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\TourInAppMarketplace' => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/TourInAppMarketplace.php',
    'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\WooCommercePayments' => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/WooCommercePayments.php',
    'Automattic\\WooCommerce\\Admin\\Features\\PaymentGatewaySuggestions\\DefaultPaymentGateways' => $baseDir . '/src/Admin/Features/PaymentGatewaySuggestions/DefaultPaymentGateways.php',
    'Automattic\\WooCommerce\\Admin\\Features\\PaymentGatewaySuggestions\\EvaluateSuggestion' => $baseDir . '/src/Admin/Features/PaymentGatewaySuggestions/EvaluateSuggestion.php',
    'Automattic\\WooCommerce\\Admin\\Features\\PaymentGatewaySuggestions\\Init' => $baseDir . '/src/Admin/Features/PaymentGatewaySuggestions/Init.php',
    'Automattic\\WooCommerce\\Admin\\Features\\PaymentGatewaySuggestions\\PaymentGatewaySuggestionsDataSourcePoller' => $baseDir . '/src/Admin/Features/PaymentGatewaySuggestions/PaymentGatewaySuggestionsDataSourcePoller.php',
    'Automattic\\WooCommerce\\Admin\\Features\\PaymentGatewaySuggestions\\PaymentGatewaysController' => $baseDir . '/src/Admin/Features/PaymentGatewaySuggestions/PaymentGatewaysController.php',
    'Automattic\\WooCommerce\\Admin\\Features\\ProductBlockEditor\\BlockRegistry' => $baseDir . '/src/Admin/Features/ProductBlockEditor/BlockRegistry.php',
    'Automattic\\WooCommerce\\Admin\\Features\\ProductBlockEditor\\BlockTemplateUtils' => $baseDir . '/src/Admin/Features/ProductBlockEditor/BlockTemplateUtils.php',
    'Automattic\\WooCommerce\\Admin\\Features\\ProductBlockEditor\\Init' => $baseDir . '/src/Admin/Features/ProductBlockEditor/Init.php',
    'Automattic\\WooCommerce\\Admin\\Features\\ProductBlockEditor\\ProductFormsController' => $baseDir . '/src/Admin/Features/ProductBlockEditor/ProductFormsController.php',
    'Automattic\\WooCommerce\\Admin\\Features\\ProductBlockEditor\\ProductTemplate' => $baseDir . '/src/Admin/Features/ProductBlockEditor/ProductTemplate.php',
    'Automattic\\WooCommerce\\Admin\\Features\\ProductBlockEditor\\ProductTemplates\\GroupInterface' => $baseDir . '/src/Admin/Features/ProductBlockEditor/ProductTemplates/GroupInterface.php',
    'Automattic\\WooCommerce\\Admin\\Features\\ProductBlockEditor\\ProductTemplates\\ProductFormTemplateInterface' => $baseDir . '/src/Admin/Features/ProductBlockEditor/ProductTemplates/ProductFormTemplateInterface.php',
    'Automattic\\WooCommerce\\Admin\\Features\\ProductBlockEditor\\ProductTemplates\\SectionInterface' => $baseDir . '/src/Admin/Features/ProductBlockEditor/ProductTemplates/SectionInterface.php',
    'Automattic\\WooCommerce\\Admin\\Features\\ProductBlockEditor\\ProductTemplates\\SubsectionInterface' => $baseDir . '/src/Admin/Features/ProductBlockEditor/ProductTemplates/SubsectionInterface.php',
    'Automattic\\WooCommerce\\Admin\\Features\\ProductBlockEditor\\RedirectionController' => $baseDir . '/src/Admin/Features/ProductBlockEditor/RedirectionController.php',
    'Automattic\\WooCommerce\\Admin\\Features\\ProductBlockEditor\\Tracks' => $baseDir . '/src/Admin/Features/ProductBlockEditor/Tracks.php',
    'Automattic\\WooCommerce\\Admin\\Features\\ProductDataViews\\Init' => $baseDir . '/src/Admin/Features/ProductDataViews/Init.php',
    'Automattic\\WooCommerce\\Admin\\Features\\Settings\\Init' => $baseDir . '/src/Admin/Features/Settings/Init.php',
    'Automattic\\WooCommerce\\Admin\\Features\\Settings\\Transformer' => $baseDir . '/src/Admin/Features/Settings/Transformer.php',
    'Automattic\\WooCommerce\\Admin\\Features\\ShippingPartnerSuggestions\\DefaultShippingPartners' => $baseDir . '/src/Admin/Features/ShippingPartnerSuggestions/DefaultShippingPartners.php',
    'Automattic\\WooCommerce\\Admin\\Features\\ShippingPartnerSuggestions\\ShippingPartnerSuggestions' => $baseDir . '/src/Admin/Features/ShippingPartnerSuggestions/ShippingPartnerSuggestions.php',
    'Automattic\\WooCommerce\\Admin\\Features\\ShippingPartnerSuggestions\\ShippingPartnerSuggestionsDataSourcePoller' => $baseDir . '/src/Admin/Features/ShippingPartnerSuggestions/ShippingPartnerSuggestionsDataSourcePoller.php',
    'Automattic\\WooCommerce\\Admin\\Features\\TransientNotices' => $baseDir . '/src/Admin/Features/TransientNotices.php',
    'Automattic\\WooCommerce\\Admin\\Loader' => $baseDir . '/src/Admin/Loader.php',
    'Automattic\\WooCommerce\\Admin\\Marketing\\InstalledExtensions' => $baseDir . '/src/Admin/Marketing/InstalledExtensions.php',
    'Automattic\\WooCommerce\\Admin\\Marketing\\MarketingCampaign' => $baseDir . '/src/Admin/Marketing/MarketingCampaign.php',
    'Automattic\\WooCommerce\\Admin\\Marketing\\MarketingCampaignType' => $baseDir . '/src/Admin/Marketing/MarketingCampaignType.php',
    'Automattic\\WooCommerce\\Admin\\Marketing\\MarketingChannelInterface' => $baseDir . '/src/Admin/Marketing/MarketingChannelInterface.php',
    'Automattic\\WooCommerce\\Admin\\Marketing\\MarketingChannels' => $baseDir . '/src/Admin/Marketing/MarketingChannels.php',
    'Automattic\\WooCommerce\\Admin\\Marketing\\Price' => $baseDir . '/src/Admin/Marketing/Price.php',
    'Automattic\\WooCommerce\\Admin\\Notes\\DataStore' => $baseDir . '/src/Admin/Notes/DataStore.php',
    'Automattic\\WooCommerce\\Admin\\Notes\\Note' => $baseDir . '/src/Admin/Notes/Note.php',
    'Automattic\\WooCommerce\\Admin\\Notes\\NoteTraits' => $baseDir . '/src/Admin/Notes/NoteTraits.php',
    'Automattic\\WooCommerce\\Admin\\Notes\\Notes' => $baseDir . '/src/Admin/Notes/Notes.php',
    'Automattic\\WooCommerce\\Admin\\Notes\\NotesUnavailableException' => $baseDir . '/src/Admin/Notes/NotesUnavailableException.php',
    'Automattic\\WooCommerce\\Admin\\Overrides\\Order' => $baseDir . '/src/Admin/Overrides/Order.php',
    'Automattic\\WooCommerce\\Admin\\Overrides\\OrderRefund' => $baseDir . '/src/Admin/Overrides/OrderRefund.php',
    'Automattic\\WooCommerce\\Admin\\Overrides\\OrderTraits' => $baseDir . '/src/Admin/Overrides/OrderTraits.php',
    'Automattic\\WooCommerce\\Admin\\Overrides\\ThemeUpgrader' => $baseDir . '/src/Admin/Overrides/ThemeUpgrader.php',
    'Automattic\\WooCommerce\\Admin\\Overrides\\ThemeUpgraderSkin' => $baseDir . '/src/Admin/Overrides/ThemeUpgraderSkin.php',
    'Automattic\\WooCommerce\\Admin\\PageController' => $baseDir . '/src/Admin/PageController.php',
    'Automattic\\WooCommerce\\Admin\\PluginsHelper' => $baseDir . '/src/Admin/PluginsHelper.php',
    'Automattic\\WooCommerce\\Admin\\PluginsInstallLoggers\\AsyncPluginsInstallLogger' => $baseDir . '/src/Admin/PluginsInstallLoggers/AsyncPluginsInstallLogger.php',
    'Automattic\\WooCommerce\\Admin\\PluginsInstallLoggers\\PluginsInstallLogger' => $baseDir . '/src/Admin/PluginsInstallLoggers/PluginsInstallLogger.php',
    'Automattic\\WooCommerce\\Admin\\PluginsInstaller' => $baseDir . '/src/Admin/PluginsInstaller.php',
    'Automattic\\WooCommerce\\Admin\\PluginsProvider\\PluginsProvider' => $baseDir . '/src/Admin/PluginsProvider/PluginsProvider.php',
    'Automattic\\WooCommerce\\Admin\\PluginsProvider\\PluginsProviderInterface' => $baseDir . '/src/Admin/PluginsProvider/PluginsProviderInterface.php',
    'Automattic\\WooCommerce\\Admin\\RemoteInboxNotifications\\RemoteInboxNotificationsDataSourcePoller' => $baseDir . '/src/Admin/RemoteInboxNotifications/RemoteInboxNotificationsDataSourcePoller.php',
    'Automattic\\WooCommerce\\Admin\\RemoteInboxNotifications\\RemoteInboxNotificationsEngine' => $baseDir . '/src/Admin/RemoteInboxNotifications/RemoteInboxNotificationsEngine.php',
    'Automattic\\WooCommerce\\Admin\\RemoteInboxNotifications\\RuleProcessorInterface' => $baseDir . '/src/Admin/RemoteInboxNotifications/RuleProcessorInterface.php',
    'Automattic\\WooCommerce\\Admin\\RemoteInboxNotifications\\SpecRunner' => $baseDir . '/src/Admin/RemoteInboxNotifications/SpecRunner.php',
    'Automattic\\WooCommerce\\Admin\\RemoteInboxNotifications\\TransformerInterface' => $baseDir . '/src/Admin/RemoteInboxNotifications/TransformerInterface.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\DataSourcePoller' => $baseDir . '/src/Admin/RemoteSpecs/DataSourcePoller.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RemoteSpecsEngine' => $baseDir . '/src/Admin/RemoteSpecs/RemoteSpecsEngine.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\BaseLocationCountryRuleProcessor' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/BaseLocationCountryRuleProcessor.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\BaseLocationStateRuleProcessor' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/BaseLocationStateRuleProcessor.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\ComparisonOperation' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/ComparisonOperation.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\ContextPluginsRuleProcessor' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/ContextPluginsRuleProcessor.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\EvaluateAndGetStatus' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/EvaluateAndGetStatus.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\EvaluateOverrides' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/EvaluateOverrides.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\EvaluationLogger' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/EvaluationLogger.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\FailRuleProcessor' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/FailRuleProcessor.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\GetRuleProcessor' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/GetRuleProcessor.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\GetRuleProcessorForContext' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/GetRuleProcessorForContext.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\IsEcommerceRuleProcessor' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/IsEcommerceRuleProcessor.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\IsWooExpressRuleProcessor' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/IsWooExpressRuleProcessor.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\NotRuleProcessor' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/NotRuleProcessor.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\NoteStatusRuleProcessor' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/NoteStatusRuleProcessor.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\OnboardingProfileRuleProcessor' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/OnboardingProfileRuleProcessor.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\OptionRuleProcessor' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/OptionRuleProcessor.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\OrRuleProcessor' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/OrRuleProcessor.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\OrderCountRuleProcessor' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/OrderCountRuleProcessor.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\OrdersProvider' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/OrdersProvider.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\PassRuleProcessor' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/PassRuleProcessor.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\PluginVersionRuleProcessor' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/PluginVersionRuleProcessor.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\PluginsActivatedRuleProcessor' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/PluginsActivatedRuleProcessor.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\ProductCountRuleProcessor' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/ProductCountRuleProcessor.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\PublishAfterTimeRuleProcessor' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/PublishAfterTimeRuleProcessor.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\PublishBeforeTimeRuleProcessor' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/PublishBeforeTimeRuleProcessor.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\RuleEvaluator' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/RuleEvaluator.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\RuleProcessorInterface' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/RuleProcessorInterface.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\StoredStateRuleProcessor' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/StoredStateRuleProcessor.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\StoredStateSetupForProducts' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/StoredStateSetupForProducts.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\TotalPaymentsVolumeProcessor' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/TotalPaymentsVolumeProcessor.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\Transformers\\ArrayColumn' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/Transformers/ArrayColumn.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\Transformers\\ArrayFlatten' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/Transformers/ArrayFlatten.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\Transformers\\ArrayKeys' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/Transformers/ArrayKeys.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\Transformers\\ArraySearch' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/Transformers/ArraySearch.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\Transformers\\ArrayValues' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/Transformers/ArrayValues.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\Transformers\\Count' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/Transformers/Count.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\Transformers\\DotNotation' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/Transformers/DotNotation.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\Transformers\\PrepareUrl' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/Transformers/PrepareUrl.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\Transformers\\TransformerInterface' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/Transformers/TransformerInterface.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\Transformers\\TransformerService' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/Transformers/TransformerService.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\WCAdminActiveForProvider' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/WCAdminActiveForProvider.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\WCAdminActiveForRuleProcessor' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/WCAdminActiveForRuleProcessor.php',
    'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\WooCommerceAdminUpdatedRuleProcessor' => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/WooCommerceAdminUpdatedRuleProcessor.php',
    'Automattic\\WooCommerce\\Admin\\ReportCSVEmail' => $baseDir . '/src/Admin/ReportCSVEmail.php',
    'Automattic\\WooCommerce\\Admin\\ReportCSVExporter' => $baseDir . '/src/Admin/ReportCSVExporter.php',
    'Automattic\\WooCommerce\\Admin\\ReportExporter' => $baseDir . '/src/Admin/ReportExporter.php',
    'Automattic\\WooCommerce\\Admin\\ReportsSync' => $baseDir . '/src/Admin/ReportsSync.php',
    'Automattic\\WooCommerce\\Admin\\Schedulers\\SchedulerTraits' => $baseDir . '/src/Admin/Schedulers/SchedulerTraits.php',
    'Automattic\\WooCommerce\\Admin\\WCAdminHelper' => $baseDir . '/src/Admin/WCAdminHelper.php',
    'Automattic\\WooCommerce\\Autoloader' => $baseDir . '/src/Autoloader.php',
    'Automattic\\WooCommerce\\Blocks\\AIContent\\ContentProcessor' => $baseDir . '/src/Blocks/AIContent/ContentProcessor.php',
    'Automattic\\WooCommerce\\Blocks\\AIContent\\PatternsDictionary' => $baseDir . '/src/Blocks/AIContent/PatternsDictionary.php',
    'Automattic\\WooCommerce\\Blocks\\AIContent\\PatternsHelper' => $baseDir . '/src/Blocks/AIContent/PatternsHelper.php',
    'Automattic\\WooCommerce\\Blocks\\AIContent\\UpdatePatterns' => $baseDir . '/src/Blocks/AIContent/UpdatePatterns.php',
    'Automattic\\WooCommerce\\Blocks\\AIContent\\UpdateProducts' => $baseDir . '/src/Blocks/AIContent/UpdateProducts.php',
    'Automattic\\WooCommerce\\Blocks\\AI\\Configuration' => $baseDir . '/src/Blocks/AI/Configuration.php',
    'Automattic\\WooCommerce\\Blocks\\AI\\Connection' => $baseDir . '/src/Blocks/AI/Connection.php',
    'Automattic\\WooCommerce\\Blocks\\Assets' => $baseDir . '/src/Blocks/Assets.php',
    'Automattic\\WooCommerce\\Blocks\\AssetsController' => $baseDir . '/src/Blocks/AssetsController.php',
    'Automattic\\WooCommerce\\Blocks\\Assets\\Api' => $baseDir . '/src/Blocks/Assets/Api.php',
    'Automattic\\WooCommerce\\Blocks\\Assets\\AssetDataRegistry' => $baseDir . '/src/Blocks/Assets/AssetDataRegistry.php',
    'Automattic\\WooCommerce\\Blocks\\BlockPatterns' => $baseDir . '/src/Blocks/BlockPatterns.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTemplatesController' => $baseDir . '/src/Blocks/BlockTemplatesController.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTemplatesRegistry' => $baseDir . '/src/Blocks/BlockTemplatesRegistry.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypesController' => $baseDir . '/src/Blocks/BlockTypesController.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AbstractBlock' => $baseDir . '/src/Blocks/BlockTypes/AbstractBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AbstractDynamicBlock' => $baseDir . '/src/Blocks/BlockTypes/AbstractDynamicBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AbstractInnerBlock' => $baseDir . '/src/Blocks/BlockTypes/AbstractInnerBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AbstractProductGrid' => $baseDir . '/src/Blocks/BlockTypes/AbstractProductGrid.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Accordion\\AccordionGroup' => $baseDir . '/src/Blocks/BlockTypes/Accordion/AccordionGroup.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Accordion\\AccordionHeader' => $baseDir . '/src/Blocks/BlockTypes/Accordion/AccordionHeader.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Accordion\\AccordionItem' => $baseDir . '/src/Blocks/BlockTypes/Accordion/AccordionItem.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Accordion\\AccordionPanel' => $baseDir . '/src/Blocks/BlockTypes/Accordion/AccordionPanel.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ActiveFilters' => $baseDir . '/src/Blocks/BlockTypes/ActiveFilters.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AddToCartForm' => $baseDir . '/src/Blocks/BlockTypes/AddToCartForm.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AddToCartWithOptions\\AddToCartWithOptions' => $baseDir . '/src/Blocks/BlockTypes/AddToCartWithOptions/AddToCartWithOptions.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AddToCartWithOptions\\GroupedProductSelector' => $baseDir . '/src/Blocks/BlockTypes/AddToCartWithOptions/GroupedProductSelector.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AddToCartWithOptions\\GroupedProductSelectorItemCTA' => $baseDir . '/src/Blocks/BlockTypes/AddToCartWithOptions/GroupedProductSelectorItemCTA.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AddToCartWithOptions\\GroupedProductSelectorItemTemplate' => $baseDir . '/src/Blocks/BlockTypes/AddToCartWithOptions/GroupedProductSelectorItemTemplate.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AddToCartWithOptions\\QuantitySelector' => $baseDir . '/src/Blocks/BlockTypes/AddToCartWithOptions/QuantitySelector.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AddToCartWithOptions\\Utils' => $baseDir . '/src/Blocks/BlockTypes/AddToCartWithOptions/Utils.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AddToCartWithOptions\\VariationSelector' => $baseDir . '/src/Blocks/BlockTypes/AddToCartWithOptions/VariationSelector.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AddToCartWithOptions\\VariationSelectorAttributeName' => $baseDir . '/src/Blocks/BlockTypes/AddToCartWithOptions/VariationSelectorAttributeName.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AddToCartWithOptions\\VariationSelectorAttributeOptions' => $baseDir . '/src/Blocks/BlockTypes/AddToCartWithOptions/VariationSelectorAttributeOptions.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AddToCartWithOptions\\VariationSelectorItemTemplate' => $baseDir . '/src/Blocks/BlockTypes/AddToCartWithOptions/VariationSelectorItemTemplate.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AllProducts' => $baseDir . '/src/Blocks/BlockTypes/AllProducts.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AllReviews' => $baseDir . '/src/Blocks/BlockTypes/AllReviews.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AtomicBlock' => $baseDir . '/src/Blocks/BlockTypes/AtomicBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AttributeFilter' => $baseDir . '/src/Blocks/BlockTypes/AttributeFilter.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\BlockifiedProductDetails' => $baseDir . '/src/Blocks/BlockTypes/BlockifiedProductDetails.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Breadcrumbs' => $baseDir . '/src/Blocks/BlockTypes/Breadcrumbs.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Cart' => $baseDir . '/src/Blocks/BlockTypes/Cart.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartAcceptedPaymentMethodsBlock' => $baseDir . '/src/Blocks/BlockTypes/CartAcceptedPaymentMethodsBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartCrossSellsBlock' => $baseDir . '/src/Blocks/BlockTypes/CartCrossSellsBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartCrossSellsProductsBlock' => $baseDir . '/src/Blocks/BlockTypes/CartCrossSellsProductsBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartExpressPaymentBlock' => $baseDir . '/src/Blocks/BlockTypes/CartExpressPaymentBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartItemsBlock' => $baseDir . '/src/Blocks/BlockTypes/CartItemsBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartLineItemsBlock' => $baseDir . '/src/Blocks/BlockTypes/CartLineItemsBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartLink' => $baseDir . '/src/Blocks/BlockTypes/CartLink.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartOrderSummaryBlock' => $baseDir . '/src/Blocks/BlockTypes/CartOrderSummaryBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartOrderSummaryCouponFormBlock' => $baseDir . '/src/Blocks/BlockTypes/CartOrderSummaryCouponFormBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartOrderSummaryDiscountBlock' => $baseDir . '/src/Blocks/BlockTypes/CartOrderSummaryDiscountBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartOrderSummaryFeeBlock' => $baseDir . '/src/Blocks/BlockTypes/CartOrderSummaryFeeBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartOrderSummaryHeadingBlock' => $baseDir . '/src/Blocks/BlockTypes/CartOrderSummaryHeadingBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartOrderSummaryShippingBlock' => $baseDir . '/src/Blocks/BlockTypes/CartOrderSummaryShippingBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartOrderSummarySubtotalBlock' => $baseDir . '/src/Blocks/BlockTypes/CartOrderSummarySubtotalBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartOrderSummaryTaxesBlock' => $baseDir . '/src/Blocks/BlockTypes/CartOrderSummaryTaxesBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartOrderSummaryTotalsBlock' => $baseDir . '/src/Blocks/BlockTypes/CartOrderSummaryTotalsBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartTotalsBlock' => $baseDir . '/src/Blocks/BlockTypes/CartTotalsBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CatalogSorting' => $baseDir . '/src/Blocks/BlockTypes/CatalogSorting.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Checkout' => $baseDir . '/src/Blocks/BlockTypes/Checkout.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutActionsBlock' => $baseDir . '/src/Blocks/BlockTypes/CheckoutActionsBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutAdditionalInformationBlock' => $baseDir . '/src/Blocks/BlockTypes/CheckoutAdditionalInformationBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutBillingAddressBlock' => $baseDir . '/src/Blocks/BlockTypes/CheckoutBillingAddressBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutContactInformationBlock' => $baseDir . '/src/Blocks/BlockTypes/CheckoutContactInformationBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutExpressPaymentBlock' => $baseDir . '/src/Blocks/BlockTypes/CheckoutExpressPaymentBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutFieldsBlock' => $baseDir . '/src/Blocks/BlockTypes/CheckoutFieldsBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutOrderNoteBlock' => $baseDir . '/src/Blocks/BlockTypes/CheckoutOrderNoteBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutOrderSummaryBlock' => $baseDir . '/src/Blocks/BlockTypes/CheckoutOrderSummaryBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutOrderSummaryCartItemsBlock' => $baseDir . '/src/Blocks/BlockTypes/CheckoutOrderSummaryCartItemsBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutOrderSummaryCouponFormBlock' => $baseDir . '/src/Blocks/BlockTypes/CheckoutOrderSummaryCouponFormBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutOrderSummaryDiscountBlock' => $baseDir . '/src/Blocks/BlockTypes/CheckoutOrderSummaryDiscountBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutOrderSummaryFeeBlock' => $baseDir . '/src/Blocks/BlockTypes/CheckoutOrderSummaryFeeBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutOrderSummaryShippingBlock' => $baseDir . '/src/Blocks/BlockTypes/CheckoutOrderSummaryShippingBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutOrderSummarySubtotalBlock' => $baseDir . '/src/Blocks/BlockTypes/CheckoutOrderSummarySubtotalBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutOrderSummaryTaxesBlock' => $baseDir . '/src/Blocks/BlockTypes/CheckoutOrderSummaryTaxesBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutOrderSummaryTotalsBlock' => $baseDir . '/src/Blocks/BlockTypes/CheckoutOrderSummaryTotalsBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutPaymentBlock' => $baseDir . '/src/Blocks/BlockTypes/CheckoutPaymentBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutPickupOptionsBlock' => $baseDir . '/src/Blocks/BlockTypes/CheckoutPickupOptionsBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutShippingAddressBlock' => $baseDir . '/src/Blocks/BlockTypes/CheckoutShippingAddressBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutShippingMethodBlock' => $baseDir . '/src/Blocks/BlockTypes/CheckoutShippingMethodBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutShippingMethodsBlock' => $baseDir . '/src/Blocks/BlockTypes/CheckoutShippingMethodsBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutTermsBlock' => $baseDir . '/src/Blocks/BlockTypes/CheckoutTermsBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutTotalsBlock' => $baseDir . '/src/Blocks/BlockTypes/CheckoutTotalsBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ClassicShortcode' => $baseDir . '/src/Blocks/BlockTypes/ClassicShortcode.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ClassicTemplate' => $baseDir . '/src/Blocks/BlockTypes/ClassicTemplate.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ComingSoon' => $baseDir . '/src/Blocks/BlockTypes/ComingSoon.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CustomerAccount' => $baseDir . '/src/Blocks/BlockTypes/CustomerAccount.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\EmptyCartBlock' => $baseDir . '/src/Blocks/BlockTypes/EmptyCartBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\EmptyMiniCartContentsBlock' => $baseDir . '/src/Blocks/BlockTypes/EmptyMiniCartContentsBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\EnableBlockJsonAssetsTrait' => $baseDir . '/src/Blocks/BlockTypes/EnableBlockJsonAssetsTrait.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\FeaturedCategory' => $baseDir . '/src/Blocks/BlockTypes/FeaturedCategory.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\FeaturedItem' => $baseDir . '/src/Blocks/BlockTypes/FeaturedItem.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\FeaturedProduct' => $baseDir . '/src/Blocks/BlockTypes/FeaturedProduct.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\FilledCartBlock' => $baseDir . '/src/Blocks/BlockTypes/FilledCartBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\FilledMiniCartContentsBlock' => $baseDir . '/src/Blocks/BlockTypes/FilledMiniCartContentsBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\FilterWrapper' => $baseDir . '/src/Blocks/BlockTypes/FilterWrapper.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\HandpickedProducts' => $baseDir . '/src/Blocks/BlockTypes/HandpickedProducts.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\MiniCart' => $baseDir . '/src/Blocks/BlockTypes/MiniCart.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\MiniCartCartButtonBlock' => $baseDir . '/src/Blocks/BlockTypes/MiniCartCartButtonBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\MiniCartCheckoutButtonBlock' => $baseDir . '/src/Blocks/BlockTypes/MiniCartCheckoutButtonBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\MiniCartContents' => $baseDir . '/src/Blocks/BlockTypes/MiniCartContents.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\MiniCartFooterBlock' => $baseDir . '/src/Blocks/BlockTypes/MiniCartFooterBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\MiniCartItemsBlock' => $baseDir . '/src/Blocks/BlockTypes/MiniCartItemsBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\MiniCartProductsTableBlock' => $baseDir . '/src/Blocks/BlockTypes/MiniCartProductsTableBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\MiniCartShoppingButtonBlock' => $baseDir . '/src/Blocks/BlockTypes/MiniCartShoppingButtonBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\MiniCartTitleBlock' => $baseDir . '/src/Blocks/BlockTypes/MiniCartTitleBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\MiniCartTitleItemsCounterBlock' => $baseDir . '/src/Blocks/BlockTypes/MiniCartTitleItemsCounterBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\MiniCartTitleLabelBlock' => $baseDir . '/src/Blocks/BlockTypes/MiniCartTitleLabelBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\OrderConfirmation\\AbstractOrderConfirmationBlock' => $baseDir . '/src/Blocks/BlockTypes/OrderConfirmation/AbstractOrderConfirmationBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\OrderConfirmation\\AdditionalFields' => $baseDir . '/src/Blocks/BlockTypes/OrderConfirmation/AdditionalFields.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\OrderConfirmation\\AdditionalFieldsWrapper' => $baseDir . '/src/Blocks/BlockTypes/OrderConfirmation/AdditionalFieldsWrapper.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\OrderConfirmation\\AdditionalInformation' => $baseDir . '/src/Blocks/BlockTypes/OrderConfirmation/AdditionalInformation.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\OrderConfirmation\\BillingAddress' => $baseDir . '/src/Blocks/BlockTypes/OrderConfirmation/BillingAddress.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\OrderConfirmation\\BillingWrapper' => $baseDir . '/src/Blocks/BlockTypes/OrderConfirmation/BillingWrapper.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\OrderConfirmation\\CreateAccount' => $baseDir . '/src/Blocks/BlockTypes/OrderConfirmation/CreateAccount.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\OrderConfirmation\\Downloads' => $baseDir . '/src/Blocks/BlockTypes/OrderConfirmation/Downloads.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\OrderConfirmation\\DownloadsWrapper' => $baseDir . '/src/Blocks/BlockTypes/OrderConfirmation/DownloadsWrapper.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\OrderConfirmation\\ShippingAddress' => $baseDir . '/src/Blocks/BlockTypes/OrderConfirmation/ShippingAddress.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\OrderConfirmation\\ShippingWrapper' => $baseDir . '/src/Blocks/BlockTypes/OrderConfirmation/ShippingWrapper.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\OrderConfirmation\\Status' => $baseDir . '/src/Blocks/BlockTypes/OrderConfirmation/Status.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\OrderConfirmation\\Summary' => $baseDir . '/src/Blocks/BlockTypes/OrderConfirmation/Summary.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\OrderConfirmation\\Totals' => $baseDir . '/src/Blocks/BlockTypes/OrderConfirmation/Totals.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\OrderConfirmation\\TotalsWrapper' => $baseDir . '/src/Blocks/BlockTypes/OrderConfirmation/TotalsWrapper.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\PageContentWrapper' => $baseDir . '/src/Blocks/BlockTypes/PageContentWrapper.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\PriceFilter' => $baseDir . '/src/Blocks/BlockTypes/PriceFilter.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProceedToCheckoutBlock' => $baseDir . '/src/Blocks/BlockTypes/ProceedToCheckoutBlock.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductAverageRating' => $baseDir . '/src/Blocks/BlockTypes/ProductAverageRating.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductBestSellers' => $baseDir . '/src/Blocks/BlockTypes/ProductBestSellers.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductButton' => $baseDir . '/src/Blocks/BlockTypes/ProductButton.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductCategories' => $baseDir . '/src/Blocks/BlockTypes/ProductCategories.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductCategory' => $baseDir . '/src/Blocks/BlockTypes/ProductCategory.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductCollection\\Controller' => $baseDir . '/src/Blocks/BlockTypes/ProductCollection/Controller.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductCollection\\HandlerRegistry' => $baseDir . '/src/Blocks/BlockTypes/ProductCollection/HandlerRegistry.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductCollection\\NoResults' => $baseDir . '/src/Blocks/BlockTypes/ProductCollection/NoResults.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductCollection\\QueryBuilder' => $baseDir . '/src/Blocks/BlockTypes/ProductCollection/QueryBuilder.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductCollection\\Renderer' => $baseDir . '/src/Blocks/BlockTypes/ProductCollection/Renderer.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductCollection\\Utils' => $baseDir . '/src/Blocks/BlockTypes/ProductCollection/Utils.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductDescription' => $baseDir . '/src/Blocks/BlockTypes/ProductDescription.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductDetails' => $baseDir . '/src/Blocks/BlockTypes/ProductDetails.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductFilterActive' => $baseDir . '/src/Blocks/BlockTypes/ProductFilterActive.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductFilterAttribute' => $baseDir . '/src/Blocks/BlockTypes/ProductFilterAttribute.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductFilterCheckboxList' => $baseDir . '/src/Blocks/BlockTypes/ProductFilterCheckboxList.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductFilterChips' => $baseDir . '/src/Blocks/BlockTypes/ProductFilterChips.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductFilterClearButton' => $baseDir . '/src/Blocks/BlockTypes/ProductFilterClearButton.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductFilterPrice' => $baseDir . '/src/Blocks/BlockTypes/ProductFilterPrice.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductFilterPriceSlider' => $baseDir . '/src/Blocks/BlockTypes/ProductFilterPriceSlider.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductFilterRating' => $baseDir . '/src/Blocks/BlockTypes/ProductFilterRating.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductFilterRemovableChips' => $baseDir . '/src/Blocks/BlockTypes/ProductFilterRemovableChips.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductFilterStatus' => $baseDir . '/src/Blocks/BlockTypes/ProductFilterStatus.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductFilters' => $baseDir . '/src/Blocks/BlockTypes/ProductFilters.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductGallery' => $baseDir . '/src/Blocks/BlockTypes/ProductGallery.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductGalleryLargeImage' => $baseDir . '/src/Blocks/BlockTypes/ProductGalleryLargeImage.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductGalleryLargeImageNextPrevious' => $baseDir . '/src/Blocks/BlockTypes/ProductGalleryLargeImageNextPrevious.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductGalleryThumbnails' => $baseDir . '/src/Blocks/BlockTypes/ProductGalleryThumbnails.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductImage' => $baseDir . '/src/Blocks/BlockTypes/ProductImage.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductImageGallery' => $baseDir . '/src/Blocks/BlockTypes/ProductImageGallery.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductMeta' => $baseDir . '/src/Blocks/BlockTypes/ProductMeta.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductNew' => $baseDir . '/src/Blocks/BlockTypes/ProductNew.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductOnSale' => $baseDir . '/src/Blocks/BlockTypes/ProductOnSale.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductPrice' => $baseDir . '/src/Blocks/BlockTypes/ProductPrice.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductQuery' => $baseDir . '/src/Blocks/BlockTypes/ProductQuery.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductRating' => $baseDir . '/src/Blocks/BlockTypes/ProductRating.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductRatingCounter' => $baseDir . '/src/Blocks/BlockTypes/ProductRatingCounter.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductRatingStars' => $baseDir . '/src/Blocks/BlockTypes/ProductRatingStars.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductResultsCount' => $baseDir . '/src/Blocks/BlockTypes/ProductResultsCount.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductReviews' => $baseDir . '/src/Blocks/BlockTypes/ProductReviews.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductSKU' => $baseDir . '/src/Blocks/BlockTypes/ProductSKU.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductSaleBadge' => $baseDir . '/src/Blocks/BlockTypes/ProductSaleBadge.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductSearch' => $baseDir . '/src/Blocks/BlockTypes/ProductSearch.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductSpecifications' => $baseDir . '/src/Blocks/BlockTypes/ProductSpecifications.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductStockIndicator' => $baseDir . '/src/Blocks/BlockTypes/ProductStockIndicator.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductSummary' => $baseDir . '/src/Blocks/BlockTypes/ProductSummary.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductTag' => $baseDir . '/src/Blocks/BlockTypes/ProductTag.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductTemplate' => $baseDir . '/src/Blocks/BlockTypes/ProductTemplate.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductTitle' => $baseDir . '/src/Blocks/BlockTypes/ProductTitle.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductTopRated' => $baseDir . '/src/Blocks/BlockTypes/ProductTopRated.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductsByAttribute' => $baseDir . '/src/Blocks/BlockTypes/ProductsByAttribute.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\RatingFilter' => $baseDir . '/src/Blocks/BlockTypes/RatingFilter.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\RelatedProducts' => $baseDir . '/src/Blocks/BlockTypes/RelatedProducts.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ReviewsByCategory' => $baseDir . '/src/Blocks/BlockTypes/ReviewsByCategory.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ReviewsByProduct' => $baseDir . '/src/Blocks/BlockTypes/ReviewsByProduct.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Reviews\\ProductReviewAuthorName' => $baseDir . '/src/Blocks/BlockTypes/Reviews/ProductReviewAuthorName.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Reviews\\ProductReviewContent' => $baseDir . '/src/Blocks/BlockTypes/Reviews/ProductReviewContent.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Reviews\\ProductReviewDate' => $baseDir . '/src/Blocks/BlockTypes/Reviews/ProductReviewDate.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Reviews\\ProductReviewForm' => $baseDir . '/src/Blocks/BlockTypes/Reviews/ProductReviewForm.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Reviews\\ProductReviewRating' => $baseDir . '/src/Blocks/BlockTypes/Reviews/ProductReviewRating.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Reviews\\ProductReviewTemplate' => $baseDir . '/src/Blocks/BlockTypes/Reviews/ProductReviewTemplate.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Reviews\\ProductReviews' => $baseDir . '/src/Blocks/BlockTypes/Reviews/ProductReviews.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Reviews\\ProductReviewsPagination' => $baseDir . '/src/Blocks/BlockTypes/Reviews/ProductReviewsPagination.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Reviews\\ProductReviewsPaginationNext' => $baseDir . '/src/Blocks/BlockTypes/Reviews/ProductReviewsPaginationNext.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Reviews\\ProductReviewsPaginationNumbers' => $baseDir . '/src/Blocks/BlockTypes/Reviews/ProductReviewsPaginationNumbers.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Reviews\\ProductReviewsPaginationPrevious' => $baseDir . '/src/Blocks/BlockTypes/Reviews/ProductReviewsPaginationPrevious.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Reviews\\ProductReviewsTitle' => $baseDir . '/src/Blocks/BlockTypes/Reviews/ProductReviewsTitle.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\SingleProduct' => $baseDir . '/src/Blocks/BlockTypes/SingleProduct.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\StockFilter' => $baseDir . '/src/Blocks/BlockTypes/StockFilter.php',
    'Automattic\\WooCommerce\\Blocks\\BlockTypes\\StoreNotices' => $baseDir . '/src/Blocks/BlockTypes/StoreNotices.php',
    'Automattic\\WooCommerce\\Blocks\\Domain\\Bootstrap' => $baseDir . '/src/Blocks/Domain/Bootstrap.php',
    'Automattic\\WooCommerce\\Blocks\\Domain\\Package' => $baseDir . '/src/Blocks/Domain/Package.php',
    'Automattic\\WooCommerce\\Blocks\\Domain\\Services\\CheckoutFields' => $baseDir . '/src/Blocks/Domain/Services/CheckoutFields.php',
    'Automattic\\WooCommerce\\Blocks\\Domain\\Services\\CheckoutFieldsAdmin' => $baseDir . '/src/Blocks/Domain/Services/CheckoutFieldsAdmin.php',
    'Automattic\\WooCommerce\\Blocks\\Domain\\Services\\CheckoutFieldsFrontend' => $baseDir . '/src/Blocks/Domain/Services/CheckoutFieldsFrontend.php',
    'Automattic\\WooCommerce\\Blocks\\Domain\\Services\\CheckoutFieldsSchema\\DocumentObject' => $baseDir . '/src/Blocks/Domain/Services/CheckoutFieldsSchema/DocumentObject.php',
    'Automattic\\WooCommerce\\Blocks\\Domain\\Services\\CheckoutFieldsSchema\\Validation' => $baseDir . '/src/Blocks/Domain/Services/CheckoutFieldsSchema/Validation.php',
    'Automattic\\WooCommerce\\Blocks\\Domain\\Services\\CreateAccount' => $baseDir . '/src/Blocks/Domain/Services/CreateAccount.php',
    'Automattic\\WooCommerce\\Blocks\\Domain\\Services\\DraftOrders' => $baseDir . '/src/Blocks/Domain/Services/DraftOrders.php',
    'Automattic\\WooCommerce\\Blocks\\Domain\\Services\\Email\\CustomerNewAccount' => $baseDir . '/src/Blocks/Domain/Services/Email/CustomerNewAccount.php',
    'Automattic\\WooCommerce\\Blocks\\Domain\\Services\\FeatureGating' => $baseDir . '/src/Blocks/Domain/Services/FeatureGating.php',
    'Automattic\\WooCommerce\\Blocks\\Domain\\Services\\GoogleAnalytics' => $baseDir . '/src/Blocks/Domain/Services/GoogleAnalytics.php',
    'Automattic\\WooCommerce\\Blocks\\Domain\\Services\\Hydration' => $baseDir . '/src/Blocks/Domain/Services/Hydration.php',
    'Automattic\\WooCommerce\\Blocks\\Domain\\Services\\Notices' => $baseDir . '/src/Blocks/Domain/Services/Notices.php',
    'Automattic\\WooCommerce\\Blocks\\Images\\Pexels' => $baseDir . '/src/Blocks/Images/Pexels.php',
    'Automattic\\WooCommerce\\Blocks\\InboxNotifications' => $baseDir . '/src/Blocks/InboxNotifications.php',
    'Automattic\\WooCommerce\\Blocks\\Installer' => $baseDir . '/src/Blocks/Installer.php',
    'Automattic\\WooCommerce\\Blocks\\Integrations\\IntegrationInterface' => $baseDir . '/src/Blocks/Integrations/IntegrationInterface.php',
    'Automattic\\WooCommerce\\Blocks\\Integrations\\IntegrationRegistry' => $baseDir . '/src/Blocks/Integrations/IntegrationRegistry.php',
    'Automattic\\WooCommerce\\Blocks\\Library' => $baseDir . '/src/Blocks/Library.php',
    'Automattic\\WooCommerce\\Blocks\\Options' => $baseDir . '/src/Blocks/Options.php',
    'Automattic\\WooCommerce\\Blocks\\Package' => $baseDir . '/src/Blocks/Package.php',
    'Automattic\\WooCommerce\\Blocks\\Patterns\\AIPatterns' => $baseDir . '/src/Blocks/Patterns/AIPatterns.php',
    'Automattic\\WooCommerce\\Blocks\\Patterns\\PTKClient' => $baseDir . '/src/Blocks/Patterns/PTKClient.php',
    'Automattic\\WooCommerce\\Blocks\\Patterns\\PTKPatternsStore' => $baseDir . '/src/Blocks/Patterns/PTKPatternsStore.php',
    'Automattic\\WooCommerce\\Blocks\\Patterns\\PatternRegistry' => $baseDir . '/src/Blocks/Patterns/PatternRegistry.php',
    'Automattic\\WooCommerce\\Blocks\\Payments\\Api' => $baseDir . '/src/Blocks/Payments/Api.php',
    'Automattic\\WooCommerce\\Blocks\\Payments\\Integrations\\AbstractPaymentMethodType' => $baseDir . '/src/Blocks/Payments/Integrations/AbstractPaymentMethodType.php',
    'Automattic\\WooCommerce\\Blocks\\Payments\\Integrations\\BankTransfer' => $baseDir . '/src/Blocks/Payments/Integrations/BankTransfer.php',
    'Automattic\\WooCommerce\\Blocks\\Payments\\Integrations\\CashOnDelivery' => $baseDir . '/src/Blocks/Payments/Integrations/CashOnDelivery.php',
    'Automattic\\WooCommerce\\Blocks\\Payments\\Integrations\\Cheque' => $baseDir . '/src/Blocks/Payments/Integrations/Cheque.php',
    'Automattic\\WooCommerce\\Blocks\\Payments\\Integrations\\PayPal' => $baseDir . '/src/Blocks/Payments/Integrations/PayPal.php',
    'Automattic\\WooCommerce\\Blocks\\Payments\\PaymentMethodRegistry' => $baseDir . '/src/Blocks/Payments/PaymentMethodRegistry.php',
    'Automattic\\WooCommerce\\Blocks\\Payments\\PaymentMethodTypeInterface' => $baseDir . '/src/Blocks/Payments/PaymentMethodTypeInterface.php',
    'Automattic\\WooCommerce\\Blocks\\QueryFilters' => $baseDir . '/src/Blocks/QueryFilters.php',
    'Automattic\\WooCommerce\\Blocks\\Registry\\AbstractDependencyType' => $baseDir . '/src/Blocks/Registry/AbstractDependencyType.php',
    'Automattic\\WooCommerce\\Blocks\\Registry\\Container' => $baseDir . '/src/Blocks/Registry/Container.php',
    'Automattic\\WooCommerce\\Blocks\\Registry\\FactoryType' => $baseDir . '/src/Blocks/Registry/FactoryType.php',
    'Automattic\\WooCommerce\\Blocks\\Registry\\SharedType' => $baseDir . '/src/Blocks/Registry/SharedType.php',
    'Automattic\\WooCommerce\\Blocks\\Shipping\\PickupLocation' => $baseDir . '/src/Blocks/Shipping/PickupLocation.php',
    'Automattic\\WooCommerce\\Blocks\\Shipping\\ShippingController' => $baseDir . '/src/Blocks/Shipping/ShippingController.php',
    'Automattic\\WooCommerce\\Blocks\\TemplateOptions' => $baseDir . '/src/Blocks/TemplateOptions.php',
    'Automattic\\WooCommerce\\Blocks\\Templates\\AbstractPageTemplate' => $baseDir . '/src/Blocks/Templates/AbstractPageTemplate.php',
    'Automattic\\WooCommerce\\Blocks\\Templates\\AbstractTemplate' => $baseDir . '/src/Blocks/Templates/AbstractTemplate.php',
    'Automattic\\WooCommerce\\Blocks\\Templates\\AbstractTemplateCompatibility' => $baseDir . '/src/Blocks/Templates/AbstractTemplateCompatibility.php',
    'Automattic\\WooCommerce\\Blocks\\Templates\\AbstractTemplatePart' => $baseDir . '/src/Blocks/Templates/AbstractTemplatePart.php',
    'Automattic\\WooCommerce\\Blocks\\Templates\\ArchiveProductTemplatesCompatibility' => $baseDir . '/src/Blocks/Templates/ArchiveProductTemplatesCompatibility.php',
    'Automattic\\WooCommerce\\Blocks\\Templates\\CartTemplate' => $baseDir . '/src/Blocks/Templates/CartTemplate.php',
    'Automattic\\WooCommerce\\Blocks\\Templates\\CheckoutHeaderTemplate' => $baseDir . '/src/Blocks/Templates/CheckoutHeaderTemplate.php',
    'Automattic\\WooCommerce\\Blocks\\Templates\\CheckoutTemplate' => $baseDir . '/src/Blocks/Templates/CheckoutTemplate.php',
    'Automattic\\WooCommerce\\Blocks\\Templates\\ClassicTemplatesCompatibility' => $baseDir . '/src/Blocks/Templates/ClassicTemplatesCompatibility.php',
    'Automattic\\WooCommerce\\Blocks\\Templates\\ComingSoonSocialLinksTemplate' => $baseDir . '/src/Blocks/Templates/ComingSoonSocialLinksTemplate.php',
    'Automattic\\WooCommerce\\Blocks\\Templates\\ComingSoonTemplate' => $baseDir . '/src/Blocks/Templates/ComingSoonTemplate.php',
    'Automattic\\WooCommerce\\Blocks\\Templates\\ExternalProductAddToCartWithOptionsTemplate' => $baseDir . '/src/Blocks/Templates/ExternalProductAddToCartWithOptionsTemplate.php',
    'Automattic\\WooCommerce\\Blocks\\Templates\\GroupedProductAddToCartWithOptionsTemplate' => $baseDir . '/src/Blocks/Templates/GroupedProductAddToCartWithOptionsTemplate.php',
    'Automattic\\WooCommerce\\Blocks\\Templates\\MiniCartTemplate' => $baseDir . '/src/Blocks/Templates/MiniCartTemplate.php',
    'Automattic\\WooCommerce\\Blocks\\Templates\\OrderConfirmationTemplate' => $baseDir . '/src/Blocks/Templates/OrderConfirmationTemplate.php',
    'Automattic\\WooCommerce\\Blocks\\Templates\\ProductAttributeTemplate' => $baseDir . '/src/Blocks/Templates/ProductAttributeTemplate.php',
    'Automattic\\WooCommerce\\Blocks\\Templates\\ProductCatalogTemplate' => $baseDir . '/src/Blocks/Templates/ProductCatalogTemplate.php',
    'Automattic\\WooCommerce\\Blocks\\Templates\\ProductCategoryTemplate' => $baseDir . '/src/Blocks/Templates/ProductCategoryTemplate.php',
    'Automattic\\WooCommerce\\Blocks\\Templates\\ProductSearchResultsTemplate' => $baseDir . '/src/Blocks/Templates/ProductSearchResultsTemplate.php',
    'Automattic\\WooCommerce\\Blocks\\Templates\\ProductTagTemplate' => $baseDir . '/src/Blocks/Templates/ProductTagTemplate.php',
    'Automattic\\WooCommerce\\Blocks\\Templates\\SimpleProductAddToCartWithOptionsTemplate' => $baseDir . '/src/Blocks/Templates/SimpleProductAddToCartWithOptionsTemplate.php',
    'Automattic\\WooCommerce\\Blocks\\Templates\\SingleProductTemplate' => $baseDir . '/src/Blocks/Templates/SingleProductTemplate.php',
    'Automattic\\WooCommerce\\Blocks\\Templates\\SingleProductTemplateCompatibility' => $baseDir . '/src/Blocks/Templates/SingleProductTemplateCompatibility.php',
    'Automattic\\WooCommerce\\Blocks\\Templates\\VariableProductAddToCartWithOptionsTemplate' => $baseDir . '/src/Blocks/Templates/VariableProductAddToCartWithOptionsTemplate.php',
    'Automattic\\WooCommerce\\Blocks\\Utils\\BlockHooksTrait' => $baseDir . '/src/Blocks/Utils/BlockHooksTrait.php',
    'Automattic\\WooCommerce\\Blocks\\Utils\\BlockTemplateUtils' => $baseDir . '/src/Blocks/Utils/BlockTemplateUtils.php',
    'Automattic\\WooCommerce\\Blocks\\Utils\\BlocksWpQuery' => $baseDir . '/src/Blocks/Utils/BlocksWpQuery.php',
    'Automattic\\WooCommerce\\Blocks\\Utils\\CartCheckoutUtils' => $baseDir . '/src/Blocks/Utils/CartCheckoutUtils.php',
    'Automattic\\WooCommerce\\Blocks\\Utils\\MiniCartUtils' => $baseDir . '/src/Blocks/Utils/MiniCartUtils.php',
    'Automattic\\WooCommerce\\Blocks\\Utils\\ProductAvailabilityUtils' => $baseDir . '/src/Blocks/Utils/ProductAvailabilityUtils.php',
    'Automattic\\WooCommerce\\Blocks\\Utils\\ProductGalleryUtils' => $baseDir . '/src/Blocks/Utils/ProductGalleryUtils.php',
    'Automattic\\WooCommerce\\Blocks\\Utils\\StyleAttributesUtils' => $baseDir . '/src/Blocks/Utils/StyleAttributesUtils.php',
    'Automattic\\WooCommerce\\Blocks\\Utils\\Utils' => $baseDir . '/src/Blocks/Utils/Utils.php',
    'Automattic\\WooCommerce\\Blueprint\\BuiltInExporters' => $baseDir . '/packages/blueprint/src/BuiltInExporters.php',
    'Automattic\\WooCommerce\\Blueprint\\BuiltInStepProcessors' => $baseDir . '/packages/blueprint/src/BuiltInStepProcessors.php',
    'Automattic\\WooCommerce\\Blueprint\\ClassExtractor' => $baseDir . '/packages/blueprint/src/ClassExtractor.php',
    'Automattic\\WooCommerce\\Blueprint\\Cli' => $baseDir . '/packages/blueprint/src/Cli.php',
    'Automattic\\WooCommerce\\Blueprint\\Cli\\ExportCli' => $baseDir . '/packages/blueprint/src/Cli/ExportCli.php',
    'Automattic\\WooCommerce\\Blueprint\\Cli\\ImportCli' => $baseDir . '/packages/blueprint/src/Cli/ImportCli.php',
    'Automattic\\WooCommerce\\Blueprint\\ExportSchema' => $baseDir . '/packages/blueprint/src/ExportSchema.php',
    'Automattic\\WooCommerce\\Blueprint\\Exporters\\ExportInstallPluginSteps' => $baseDir . '/packages/blueprint/src/Exporters/ExportInstallPluginSteps.php',
    'Automattic\\WooCommerce\\Blueprint\\Exporters\\ExportInstallThemeSteps' => $baseDir . '/packages/blueprint/src/Exporters/ExportInstallThemeSteps.php',
    'Automattic\\WooCommerce\\Blueprint\\Exporters\\HasAlias' => $baseDir . '/packages/blueprint/src/Exporters/HasAlias.php',
    'Automattic\\WooCommerce\\Blueprint\\Exporters\\StepExporter' => $baseDir . '/packages/blueprint/src/Exporters/StepExporter.php',
    'Automattic\\WooCommerce\\Blueprint\\ImportSchema' => $baseDir . '/packages/blueprint/src/ImportSchema.php',
    'Automattic\\WooCommerce\\Blueprint\\ImportStep' => $baseDir . '/packages/blueprint/src/ImportStep.php',
    'Automattic\\WooCommerce\\Blueprint\\Importers\\ImportActivatePlugin' => $baseDir . '/packages/blueprint/src/Importers/ImportActivatePlugin.php',
    'Automattic\\WooCommerce\\Blueprint\\Importers\\ImportActivateTheme' => $baseDir . '/packages/blueprint/src/Importers/ImportActivateTheme.php',
    'Automattic\\WooCommerce\\Blueprint\\Importers\\ImportInstallPlugin' => $baseDir . '/packages/blueprint/src/Importers/ImportInstallPlugin.php',
    'Automattic\\WooCommerce\\Blueprint\\Importers\\ImportInstallTheme' => $baseDir . '/packages/blueprint/src/Importers/ImportInstallTheme.php',
    'Automattic\\WooCommerce\\Blueprint\\Importers\\ImportRunSql' => $baseDir . '/packages/blueprint/src/Importers/ImportRunSql.php',
    'Automattic\\WooCommerce\\Blueprint\\Importers\\ImportSetSiteOptions' => $baseDir . '/packages/blueprint/src/Importers/ImportSetSiteOptions.php',
    'Automattic\\WooCommerce\\Blueprint\\Logger' => $baseDir . '/packages/blueprint/src/Logger.php',
    'Automattic\\WooCommerce\\Blueprint\\ResourceStorages' => $baseDir . '/packages/blueprint/src/ResourceStorages.php',
    'Automattic\\WooCommerce\\Blueprint\\ResourceStorages\\LocalPluginResourceStorage' => $baseDir . '/packages/blueprint/src/ResourceStorages/LocalPluginResourceStorage.php',
    'Automattic\\WooCommerce\\Blueprint\\ResourceStorages\\LocalThemeResourceStorage' => $baseDir . '/packages/blueprint/src/ResourceStorages/LocalThemeResourceStorage.php',
    'Automattic\\WooCommerce\\Blueprint\\ResourceStorages\\OrgPluginResourceStorage' => $baseDir . '/packages/blueprint/src/ResourceStorages/OrgPluginResourceStorage.php',
    'Automattic\\WooCommerce\\Blueprint\\ResourceStorages\\OrgThemeResourceStorage' => $baseDir . '/packages/blueprint/src/ResourceStorages/OrgThemeResourceStorage.php',
    'Automattic\\WooCommerce\\Blueprint\\ResourceStorages\\ResourceStorage' => $baseDir . '/packages/blueprint/src/ResourceStorages/ResourceStorage.php',
    'Automattic\\WooCommerce\\Blueprint\\ResultFormatters\\CliResultFormatter' => $baseDir . '/packages/blueprint/src/ResultFormatters/CliResultFormatter.php',
    'Automattic\\WooCommerce\\Blueprint\\ResultFormatters\\JsonResultFormatter' => $baseDir . '/packages/blueprint/src/ResultFormatters/JsonResultFormatter.php',
    'Automattic\\WooCommerce\\Blueprint\\Schemas\\JsonSchema' => $baseDir . '/packages/blueprint/src/Schemas/JsonSchema.php',
    'Automattic\\WooCommerce\\Blueprint\\StepProcessor' => $baseDir . '/packages/blueprint/src/StepProcessor.php',
    'Automattic\\WooCommerce\\Blueprint\\StepProcessorResult' => $baseDir . '/packages/blueprint/src/StepProcessorResult.php',
    'Automattic\\WooCommerce\\Blueprint\\Steps\\ActivatePlugin' => $baseDir . '/packages/blueprint/src/Steps/ActivatePlugin.php',
    'Automattic\\WooCommerce\\Blueprint\\Steps\\ActivateTheme' => $baseDir . '/packages/blueprint/src/Steps/ActivateTheme.php',
    'Automattic\\WooCommerce\\Blueprint\\Steps\\InstallPlugin' => $baseDir . '/packages/blueprint/src/Steps/InstallPlugin.php',
    'Automattic\\WooCommerce\\Blueprint\\Steps\\InstallTheme' => $baseDir . '/packages/blueprint/src/Steps/InstallTheme.php',
    'Automattic\\WooCommerce\\Blueprint\\Steps\\RunSql' => $baseDir . '/packages/blueprint/src/Steps/RunSql.php',
    'Automattic\\WooCommerce\\Blueprint\\Steps\\SetSiteOptions' => $baseDir . '/packages/blueprint/src/Steps/SetSiteOptions.php',
    'Automattic\\WooCommerce\\Blueprint\\Steps\\Step' => $baseDir . '/packages/blueprint/src/Steps/Step.php',
    'Automattic\\WooCommerce\\Blueprint\\UsePluginHelpers' => $baseDir . '/packages/blueprint/src/UsePluginHelpers.php',
    'Automattic\\WooCommerce\\Blueprint\\UsePubSub' => $baseDir . '/packages/blueprint/src/UsePubSub.php',
    'Automattic\\WooCommerce\\Blueprint\\UseWPFunctions' => $baseDir . '/packages/blueprint/src/UseWPFunctions.php',
    'Automattic\\WooCommerce\\Blueprint\\Util' => $baseDir . '/packages/blueprint/src/Util.php',
    'Automattic\\WooCommerce\\Caches\\OrderCache' => $baseDir . '/src/Caches/OrderCache.php',
    'Automattic\\WooCommerce\\Caches\\OrderCacheController' => $baseDir . '/src/Caches/OrderCacheController.php',
    'Automattic\\WooCommerce\\Caches\\OrderCountCache' => $baseDir . '/src/Caches/OrderCountCache.php',
    'Automattic\\WooCommerce\\Caches\\OrderCountCacheService' => $baseDir . '/src/Caches/OrderCountCacheService.php',
    'Automattic\\WooCommerce\\Caching\\CacheEngine' => $baseDir . '/src/Caching/CacheEngine.php',
    'Automattic\\WooCommerce\\Caching\\CacheException' => $baseDir . '/src/Caching/CacheException.php',
    'Automattic\\WooCommerce\\Caching\\CacheNameSpaceTrait' => $baseDir . '/src/Caching/CacheNameSpaceTrait.php',
    'Automattic\\WooCommerce\\Caching\\ObjectCache' => $baseDir . '/src/Caching/ObjectCache.php',
    'Automattic\\WooCommerce\\Caching\\WPCacheEngine' => $baseDir . '/src/Caching/WPCacheEngine.php',
    'Automattic\\WooCommerce\\Checkout\\Helpers\\ReserveStock' => $baseDir . '/src/Checkout/Helpers/ReserveStock.php',
    'Automattic\\WooCommerce\\Checkout\\Helpers\\ReserveStockException' => $baseDir . '/src/Checkout/Helpers/ReserveStockException.php',
    'Automattic\\WooCommerce\\Container' => $baseDir . '/src/Container.php',
    'Automattic\\WooCommerce\\Database\\Migrations\\CustomOrderTable\\CLIRunner' => $baseDir . '/src/Database/Migrations/CustomOrderTable/CLIRunner.php',
    'Automattic\\WooCommerce\\Database\\Migrations\\CustomOrderTable\\PostMetaToOrderMetaMigrator' => $baseDir . '/src/Database/Migrations/CustomOrderTable/PostMetaToOrderMetaMigrator.php',
    'Automattic\\WooCommerce\\Database\\Migrations\\CustomOrderTable\\PostToOrderAddressTableMigrator' => $baseDir . '/src/Database/Migrations/CustomOrderTable/PostToOrderAddressTableMigrator.php',
    'Automattic\\WooCommerce\\Database\\Migrations\\CustomOrderTable\\PostToOrderOpTableMigrator' => $baseDir . '/src/Database/Migrations/CustomOrderTable/PostToOrderOpTableMigrator.php',
    'Automattic\\WooCommerce\\Database\\Migrations\\CustomOrderTable\\PostToOrderTableMigrator' => $baseDir . '/src/Database/Migrations/CustomOrderTable/PostToOrderTableMigrator.php',
    'Automattic\\WooCommerce\\Database\\Migrations\\CustomOrderTable\\PostsToOrdersMigrationController' => $baseDir . '/src/Database/Migrations/CustomOrderTable/PostsToOrdersMigrationController.php',
    'Automattic\\WooCommerce\\Database\\Migrations\\MetaToCustomTableMigrator' => $baseDir . '/src/Database/Migrations/MetaToCustomTableMigrator.php',
    'Automattic\\WooCommerce\\Database\\Migrations\\MetaToMetaTableMigrator' => $baseDir . '/src/Database/Migrations/MetaToMetaTableMigrator.php',
    'Automattic\\WooCommerce\\Database\\Migrations\\MigrationHelper' => $baseDir . '/src/Database/Migrations/MigrationHelper.php',
    'Automattic\\WooCommerce\\Database\\Migrations\\TableMigrator' => $baseDir . '/src/Database/Migrations/TableMigrator.php',
    'Automattic\\WooCommerce\\EmailEditor\\AccessDeniedException' => $baseDir . '/packages/email-editor/src/exceptions.php',
    'Automattic\\WooCommerce\\EmailEditor\\Bootstrap' => $baseDir . '/packages/email-editor/src/class-bootstrap.php',
    'Automattic\\WooCommerce\\EmailEditor\\ConflictException' => $baseDir . '/packages/email-editor/src/exceptions.php',
    'Automattic\\WooCommerce\\EmailEditor\\Container' => $baseDir . '/packages/email-editor/src/class-container.php',
    'Automattic\\WooCommerce\\EmailEditor\\Email_Css_Inliner' => $baseDir . '/packages/email-editor/src/class-email-css-inliner.php',
    'Automattic\\WooCommerce\\EmailEditor\\Email_Editor_Container' => $baseDir . '/packages/email-editor/src/class-email-editor-container.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Dependency_Check' => $baseDir . '/packages/email-editor/src/Engine/class-dependency-check.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Email_Api_Controller' => $baseDir . '/packages/email-editor/src/Engine/class-email-api-controller.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Email_Editor' => $baseDir . '/packages/email-editor/src/Engine/class-email-editor.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Email_Styles_Schema' => $baseDir . '/packages/email-editor/src/Engine/class-email-styles-schema.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Patterns\\Abstract_Pattern' => $baseDir . '/packages/email-editor/src/Engine/Patterns/class-abstract-pattern.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Patterns\\Patterns' => $baseDir . '/packages/email-editor/src/Engine/Patterns/class-patterns.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\PersonalizationTags\\HTML_Tag_Processor' => $baseDir . '/packages/email-editor/src/Engine/PersonalizationTags/class-html-tag-processor.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\PersonalizationTags\\Personalization_Tag' => $baseDir . '/packages/email-editor/src/Engine/PersonalizationTags/class-personalization-tag.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\PersonalizationTags\\Personalization_Tags_Registry' => $baseDir . '/packages/email-editor/src/Engine/PersonalizationTags/class-personalization-tags-registry.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Personalizer' => $baseDir . '/packages/email-editor/src/Engine/class-personalizer.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Block_Renderer' => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/class-block-renderer.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Blocks_Parser' => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/class-blocks-parser.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Blocks_Registry' => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/class-blocks-registry.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Content_Renderer' => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/class-content-renderer.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Layout\\Flex_Layout_Renderer' => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/Layout/class-flex-layout-renderer.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Postprocessors\\Border_Style_Postprocessor' => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/Postprocessors/class-border-style-postprocessor.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Postprocessors\\Highlighting_Postprocessor' => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/Postprocessors/class-highlighting-postprocessor.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Postprocessors\\Postprocessor' => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/Postprocessors/interface-postprocessor.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Postprocessors\\Variables_Postprocessor' => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/Postprocessors/class-variables-postprocessor.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Preprocessors\\Blocks_Width_Preprocessor' => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/Preprocessors/class-blocks-width-preprocessor.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Preprocessors\\Cleanup_Preprocessor' => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/Preprocessors/class-cleanup-preprocessor.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Preprocessors\\Preprocessor' => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/Preprocessors/interface-preprocessor.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Preprocessors\\Quote_Preprocessor' => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/Preprocessors/class-quote-preprocessor.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Preprocessors\\Spacing_Preprocessor' => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/Preprocessors/class-spacing-preprocessor.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Preprocessors\\Typography_Preprocessor' => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/Preprocessors/class-typography-preprocessor.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Process_Manager' => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/class-process-manager.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\Css_Inliner' => $baseDir . '/packages/email-editor/src/Engine/Renderer/interface-css-inliner.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\Renderer' => $baseDir . '/packages/email-editor/src/Engine/Renderer/class-renderer.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Send_Preview_Email' => $baseDir . '/packages/email-editor/src/Engine/class-send-preview-email.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Settings_Controller' => $baseDir . '/packages/email-editor/src/Engine/class-settings-controller.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Templates\\Template' => $baseDir . '/packages/email-editor/src/Engine/Templates/class-template.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Templates\\Templates' => $baseDir . '/packages/email-editor/src/Engine/Templates/class-templates.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Templates\\Templates_Registry' => $baseDir . '/packages/email-editor/src/Engine/Templates/class-templates-registry.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\Theme_Controller' => $baseDir . '/packages/email-editor/src/Engine/class-theme-controller.php',
    'Automattic\\WooCommerce\\EmailEditor\\Engine\\User_Theme' => $baseDir . '/packages/email-editor/src/Engine/class-user-theme.php',
    'Automattic\\WooCommerce\\EmailEditor\\Exception' => $baseDir . '/packages/email-editor/src/exceptions.php',
    'Automattic\\WooCommerce\\EmailEditor\\HttpAwareException' => $baseDir . '/packages/email-editor/src/exceptions.php',
    'Automattic\\WooCommerce\\EmailEditor\\Integrations\\Core\\Initializer' => $baseDir . '/packages/email-editor/src/Integrations/Core/class-initializer.php',
    'Automattic\\WooCommerce\\EmailEditor\\Integrations\\Core\\Renderer\\Blocks\\Abstract_Block_Renderer' => $baseDir . '/packages/email-editor/src/Integrations/Core/Renderer/Blocks/class-abstract-block-renderer.php',
    'Automattic\\WooCommerce\\EmailEditor\\Integrations\\Core\\Renderer\\Blocks\\Button' => $baseDir . '/packages/email-editor/src/Integrations/Core/Renderer/Blocks/class-button.php',
    'Automattic\\WooCommerce\\EmailEditor\\Integrations\\Core\\Renderer\\Blocks\\Buttons' => $baseDir . '/packages/email-editor/src/Integrations/Core/Renderer/Blocks/class-buttons.php',
    'Automattic\\WooCommerce\\EmailEditor\\Integrations\\Core\\Renderer\\Blocks\\Column' => $baseDir . '/packages/email-editor/src/Integrations/Core/Renderer/Blocks/class-column.php',
    'Automattic\\WooCommerce\\EmailEditor\\Integrations\\Core\\Renderer\\Blocks\\Columns' => $baseDir . '/packages/email-editor/src/Integrations/Core/Renderer/Blocks/class-columns.php',
    'Automattic\\WooCommerce\\EmailEditor\\Integrations\\Core\\Renderer\\Blocks\\Fallback' => $baseDir . '/packages/email-editor/src/Integrations/Core/Renderer/Blocks/class-fallback.php',
    'Automattic\\WooCommerce\\EmailEditor\\Integrations\\Core\\Renderer\\Blocks\\Group' => $baseDir . '/packages/email-editor/src/Integrations/Core/Renderer/Blocks/class-group.php',
    'Automattic\\WooCommerce\\EmailEditor\\Integrations\\Core\\Renderer\\Blocks\\Image' => $baseDir . '/packages/email-editor/src/Integrations/Core/Renderer/Blocks/class-image.php',
    'Automattic\\WooCommerce\\EmailEditor\\Integrations\\Core\\Renderer\\Blocks\\List_Block' => $baseDir . '/packages/email-editor/src/Integrations/Core/Renderer/Blocks/class-list-block.php',
    'Automattic\\WooCommerce\\EmailEditor\\Integrations\\Core\\Renderer\\Blocks\\List_Item' => $baseDir . '/packages/email-editor/src/Integrations/Core/Renderer/Blocks/class-list-item.php',
    'Automattic\\WooCommerce\\EmailEditor\\Integrations\\Core\\Renderer\\Blocks\\Quote' => $baseDir . '/packages/email-editor/src/Integrations/Core/Renderer/Blocks/class-quote.php',
    'Automattic\\WooCommerce\\EmailEditor\\Integrations\\Core\\Renderer\\Blocks\\Text' => $baseDir . '/packages/email-editor/src/Integrations/Core/Renderer/Blocks/class-text.php',
    'Automattic\\WooCommerce\\EmailEditor\\Integrations\\Utils\\Dom_Document_Helper' => $baseDir . '/packages/email-editor/src/Integrations/Utils/class-dom-document-helper.php',
    'Automattic\\WooCommerce\\EmailEditor\\InvalidStateException' => $baseDir . '/packages/email-editor/src/exceptions.php',
    'Automattic\\WooCommerce\\EmailEditor\\NewsletterProcessingException' => $baseDir . '/packages/email-editor/src/exceptions.php',
    'Automattic\\WooCommerce\\EmailEditor\\NotFoundException' => $baseDir . '/packages/email-editor/src/exceptions.php',
    'Automattic\\WooCommerce\\EmailEditor\\Package' => $baseDir . '/packages/email-editor/src/class-package.php',
    'Automattic\\WooCommerce\\EmailEditor\\RuntimeException' => $baseDir . '/packages/email-editor/src/exceptions.php',
    'Automattic\\WooCommerce\\EmailEditor\\UnexpectedValueException' => $baseDir . '/packages/email-editor/src/exceptions.php',
    'Automattic\\WooCommerce\\EmailEditor\\Validator\\Builder' => $baseDir . '/packages/email-editor/src/Validator/class-builder.php',
    'Automattic\\WooCommerce\\EmailEditor\\Validator\\Schema' => $baseDir . '/packages/email-editor/src/Validator/class-schema.php',
    'Automattic\\WooCommerce\\EmailEditor\\Validator\\Schema\\Any_Of_Schema' => $baseDir . '/packages/email-editor/src/Validator/Schema/class-any-of-schema.php',
    'Automattic\\WooCommerce\\EmailEditor\\Validator\\Schema\\Array_Schema' => $baseDir . '/packages/email-editor/src/Validator/Schema/class-array-schema.php',
    'Automattic\\WooCommerce\\EmailEditor\\Validator\\Schema\\Boolean_Schema' => $baseDir . '/packages/email-editor/src/Validator/Schema/class-boolean-schema.php',
    'Automattic\\WooCommerce\\EmailEditor\\Validator\\Schema\\Integer_Schema' => $baseDir . '/packages/email-editor/src/Validator/Schema/class-integer-schema.php',
    'Automattic\\WooCommerce\\EmailEditor\\Validator\\Schema\\Null_Schema' => $baseDir . '/packages/email-editor/src/Validator/Schema/class-null-schema.php',
    'Automattic\\WooCommerce\\EmailEditor\\Validator\\Schema\\Number_Schema' => $baseDir . '/packages/email-editor/src/Validator/Schema/class-number-schema.php',
    'Automattic\\WooCommerce\\EmailEditor\\Validator\\Schema\\Object_Schema' => $baseDir . '/packages/email-editor/src/Validator/Schema/class-object-schema.php',
    'Automattic\\WooCommerce\\EmailEditor\\Validator\\Schema\\One_Of_Schema' => $baseDir . '/packages/email-editor/src/Validator/Schema/class-one-of-schema.php',
    'Automattic\\WooCommerce\\EmailEditor\\Validator\\Schema\\String_Schema' => $baseDir . '/packages/email-editor/src/Validator/Schema/class-string-schema.php',
    'Automattic\\WooCommerce\\EmailEditor\\Validator\\Validation_Exception' => $baseDir . '/packages/email-editor/src/Validator/class-validation-exception.php',
    'Automattic\\WooCommerce\\EmailEditor\\Validator\\Validator' => $baseDir . '/packages/email-editor/src/Validator/class-validator.php',
    'Automattic\\WooCommerce\\Enums\\CatalogVisibility' => $baseDir . '/src/Enums/CatalogVisibility.php',
    'Automattic\\WooCommerce\\Enums\\OrderInternalStatus' => $baseDir . '/src/Enums/OrderInternalStatus.php',
    'Automattic\\WooCommerce\\Enums\\OrderStatus' => $baseDir . '/src/Enums/OrderStatus.php',
    'Automattic\\WooCommerce\\Enums\\ProductStatus' => $baseDir . '/src/Enums/ProductStatus.php',
    'Automattic\\WooCommerce\\Enums\\ProductStockStatus' => $baseDir . '/src/Enums/ProductStockStatus.php',
    'Automattic\\WooCommerce\\Enums\\ProductTaxStatus' => $baseDir . '/src/Enums/ProductTaxStatus.php',
    'Automattic\\WooCommerce\\Enums\\ProductType' => $baseDir . '/src/Enums/ProductType.php',
    'Automattic\\WooCommerce\\Internal\\AddressProvider\\AddressProviderController' => $baseDir . '/src/Internal/AddressProvider/AddressProviderController.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\ActivityPanels' => $baseDir . '/src/Internal/Admin/ActivityPanels.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Analytics' => $baseDir . '/src/Internal/Admin/Analytics.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\BlockTemplates\\AbstractBlock' => $baseDir . '/src/Internal/Admin/BlockTemplates/AbstractBlock.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\BlockTemplates\\AbstractBlockTemplate' => $baseDir . '/src/Internal/Admin/BlockTemplates/AbstractBlockTemplate.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\BlockTemplates\\Block' => $baseDir . '/src/Internal/Admin/BlockTemplates/Block.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\BlockTemplates\\BlockContainerTrait' => $baseDir . '/src/Internal/Admin/BlockTemplates/BlockContainerTrait.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\BlockTemplates\\BlockFormattedTemplateTrait' => $baseDir . '/src/Internal/Admin/BlockTemplates/BlockFormattedTemplateTrait.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\BlockTemplates\\BlockTemplate' => $baseDir . '/src/Internal/Admin/BlockTemplates/BlockTemplate.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\BlockTemplates\\BlockTemplateLogger' => $baseDir . '/src/Internal/Admin/BlockTemplates/BlockTemplateLogger.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\CategoryLookup' => $baseDir . '/src/Internal/Admin/CategoryLookup.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Coupons' => $baseDir . '/src/Internal/Admin/Coupons.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\CouponsMovedTrait' => $baseDir . '/src/Internal/Admin/CouponsMovedTrait.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\CustomerEffortScoreTracks' => $baseDir . '/src/Internal/Admin/CustomerEffortScoreTracks.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\EmailImprovements\\EmailImprovements' => $baseDir . '/src/Internal/Admin/EmailImprovements/EmailImprovements.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\EmailPreview\\EmailPreview' => $baseDir . '/src/Internal/Admin/EmailPreview/EmailPreview.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\EmailPreview\\EmailPreviewRestController' => $baseDir . '/src/Internal/Admin/EmailPreview/EmailPreviewRestController.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Events' => $baseDir . '/src/Internal/Admin/Events.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\FeaturePlugin' => $baseDir . '/src/Internal/Admin/FeaturePlugin.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Homescreen' => $baseDir . '/src/Internal/Admin/Homescreen.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\ImportExport\\CSVUploadHelper' => $baseDir . '/src/Internal/Admin/ImportExport/CSVUploadHelper.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Loader' => $baseDir . '/src/Internal/Admin/Loader.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Logging\\FileV2\\File' => $baseDir . '/src/Internal/Admin/Logging/FileV2/File.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Logging\\FileV2\\FileController' => $baseDir . '/src/Internal/Admin/Logging/FileV2/FileController.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Logging\\FileV2\\FileExporter' => $baseDir . '/src/Internal/Admin/Logging/FileV2/FileExporter.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Logging\\FileV2\\FileListTable' => $baseDir . '/src/Internal/Admin/Logging/FileV2/FileListTable.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Logging\\FileV2\\SearchListTable' => $baseDir . '/src/Internal/Admin/Logging/FileV2/SearchListTable.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Logging\\LogHandlerFileV2' => $baseDir . '/src/Internal/Admin/Logging/LogHandlerFileV2.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Logging\\PageController' => $baseDir . '/src/Internal/Admin/Logging/PageController.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Logging\\Settings' => $baseDir . '/src/Internal/Admin/Logging/Settings.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Marketing' => $baseDir . '/src/Internal/Admin/Marketing.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Marketing\\MarketingSpecs' => $baseDir . '/src/Internal/Admin/Marketing/MarketingSpecs.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Marketplace' => $baseDir . '/src/Internal/Admin/Marketplace.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\MobileAppBanner' => $baseDir . '/src/Internal/Admin/MobileAppBanner.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\CustomizeStoreWithBlocks' => $baseDir . '/src/Internal/Admin/Notes/CustomizeStoreWithBlocks.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\CustomizingProductCatalog' => $baseDir . '/src/Internal/Admin/Notes/CustomizingProductCatalog.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\EUVATNumber' => $baseDir . '/src/Internal/Admin/Notes/EUVATNumber.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\EditProductsOnTheMove' => $baseDir . '/src/Internal/Admin/Notes/EditProductsOnTheMove.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\EmailImprovements' => $baseDir . '/src/Internal/Admin/Notes/EmailImprovements.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\FirstProduct' => $baseDir . '/src/Internal/Admin/Notes/FirstProduct.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\GivingFeedbackNotes' => $baseDir . '/src/Internal/Admin/Notes/GivingFeedbackNotes.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\InstallJPAndWCSPlugins' => $baseDir . '/src/Internal/Admin/Notes/InstallJPAndWCSPlugins.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\LaunchChecklist' => $baseDir . '/src/Internal/Admin/Notes/LaunchChecklist.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\MagentoMigration' => $baseDir . '/src/Internal/Admin/Notes/MagentoMigration.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\ManageOrdersOnTheGo' => $baseDir . '/src/Internal/Admin/Notes/ManageOrdersOnTheGo.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\MarketingJetpack' => $baseDir . '/src/Internal/Admin/Notes/MarketingJetpack.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\MigrateFromShopify' => $baseDir . '/src/Internal/Admin/Notes/MigrateFromShopify.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\MobileApp' => $baseDir . '/src/Internal/Admin/Notes/MobileApp.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\NewSalesRecord' => $baseDir . '/src/Internal/Admin/Notes/NewSalesRecord.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\OnboardingPayments' => $baseDir . '/src/Internal/Admin/Notes/OnboardingPayments.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\OnlineClothingStore' => $baseDir . '/src/Internal/Admin/Notes/OnlineClothingStore.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\OrderMilestones' => $baseDir . '/src/Internal/Admin/Notes/OrderMilestones.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\PaymentsMoreInfoNeeded' => $baseDir . '/src/Internal/Admin/Notes/PaymentsMoreInfoNeeded.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\PaymentsRemindMeLater' => $baseDir . '/src/Internal/Admin/Notes/PaymentsRemindMeLater.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\PerformanceOnMobile' => $baseDir . '/src/Internal/Admin/Notes/PerformanceOnMobile.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\PersonalizeStore' => $baseDir . '/src/Internal/Admin/Notes/PersonalizeStore.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\RealTimeOrderAlerts' => $baseDir . '/src/Internal/Admin/Notes/RealTimeOrderAlerts.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\SellingOnlineCourses' => $baseDir . '/src/Internal/Admin/Notes/SellingOnlineCourses.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\TrackingOptIn' => $baseDir . '/src/Internal/Admin/Notes/TrackingOptIn.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\UnsecuredReportFiles' => $baseDir . '/src/Internal/Admin/Notes/UnsecuredReportFiles.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\WooCommercePayments' => $baseDir . '/src/Internal/Admin/Notes/WooCommercePayments.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\WooCommerceSubscriptions' => $baseDir . '/src/Internal/Admin/Notes/WooCommerceSubscriptions.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\WooSubscriptionsNotes' => $baseDir . '/src/Internal/Admin/Notes/WooSubscriptionsNotes.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Onboarding\\Onboarding' => $baseDir . '/src/Internal/Admin/Onboarding/Onboarding.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Onboarding\\OnboardingFonts' => $baseDir . '/src/Internal/Admin/Onboarding/OnboardingFonts.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Onboarding\\OnboardingHelper' => $baseDir . '/src/Internal/Admin/Onboarding/OnboardingHelper.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Onboarding\\OnboardingIndustries' => $baseDir . '/src/Internal/Admin/Onboarding/OnboardingIndustries.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Onboarding\\OnboardingJetpack' => $baseDir . '/src/Internal/Admin/Onboarding/OnboardingJetpack.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Onboarding\\OnboardingMailchimp' => $baseDir . '/src/Internal/Admin/Onboarding/OnboardingMailchimp.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Onboarding\\OnboardingProducts' => $baseDir . '/src/Internal/Admin/Onboarding/OnboardingProducts.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Onboarding\\OnboardingProfile' => $baseDir . '/src/Internal/Admin/Onboarding/OnboardingProfile.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Onboarding\\OnboardingSetupWizard' => $baseDir . '/src/Internal/Admin/Onboarding/OnboardingSetupWizard.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Onboarding\\OnboardingSync' => $baseDir . '/src/Internal/Admin/Onboarding/OnboardingSync.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Orders\\COTRedirectionController' => $baseDir . '/src/Internal/Admin/Orders/COTRedirectionController.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Orders\\Edit' => $baseDir . '/src/Internal/Admin/Orders/Edit.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Orders\\EditLock' => $baseDir . '/src/Internal/Admin/Orders/EditLock.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Orders\\ListTable' => $baseDir . '/src/Internal/Admin/Orders/ListTable.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Orders\\MetaBoxes\\CustomMetaBox' => $baseDir . '/src/Internal/Admin/Orders/MetaBoxes/CustomMetaBox.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Orders\\MetaBoxes\\CustomerHistory' => $baseDir . '/src/Internal/Admin/Orders/MetaBoxes/CustomerHistory.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Orders\\MetaBoxes\\OrderAttribution' => $baseDir . '/src/Internal/Admin/Orders/MetaBoxes/OrderAttribution.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Orders\\MetaBoxes\\TaxonomiesMetaBox' => $baseDir . '/src/Internal/Admin/Orders/MetaBoxes/TaxonomiesMetaBox.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Orders\\PageController' => $baseDir . '/src/Internal/Admin/Orders/PageController.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Orders\\PostsRedirectionController' => $baseDir . '/src/Internal/Admin/Orders/PostsRedirectionController.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\ProductForm\\Component' => $baseDir . '/src/Internal/Admin/ProductForm/Component.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\ProductForm\\ComponentTrait' => $baseDir . '/src/Internal/Admin/ProductForm/ComponentTrait.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\ProductForm\\Field' => $baseDir . '/src/Internal/Admin/ProductForm/Field.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\ProductForm\\FormFactory' => $baseDir . '/src/Internal/Admin/ProductForm/FormFactory.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\ProductForm\\Section' => $baseDir . '/src/Internal/Admin/ProductForm/Section.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\ProductForm\\Subsection' => $baseDir . '/src/Internal/Admin/ProductForm/Subsection.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\ProductForm\\Tab' => $baseDir . '/src/Internal/Admin/ProductForm/Tab.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\ProductReviews\\Reviews' => $baseDir . '/src/Internal/Admin/ProductReviews/Reviews.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\ProductReviews\\ReviewsCommentsOverrides' => $baseDir . '/src/Internal/Admin/ProductReviews/ReviewsCommentsOverrides.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\ProductReviews\\ReviewsListTable' => $baseDir . '/src/Internal/Admin/ProductReviews/ReviewsListTable.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\ProductReviews\\ReviewsUtil' => $baseDir . '/src/Internal/Admin/ProductReviews/ReviewsUtil.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\RemoteFreeExtensions\\DefaultFreeExtensions' => $baseDir . '/src/Internal/Admin/RemoteFreeExtensions/DefaultFreeExtensions.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\RemoteFreeExtensions\\EvaluateExtension' => $baseDir . '/src/Internal/Admin/RemoteFreeExtensions/EvaluateExtension.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\RemoteFreeExtensions\\Init' => $baseDir . '/src/Internal/Admin/RemoteFreeExtensions/Init.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\RemoteFreeExtensions\\ProcessCoreProfilerPluginInstallOptions' => $baseDir . '/src/Internal/Admin/RemoteFreeExtensions/ProcessCoreProfilerPluginInstallOptions.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\RemoteFreeExtensions\\RemoteFreeExtensionsDataSourcePoller' => $baseDir . '/src/Internal/Admin/RemoteFreeExtensions/RemoteFreeExtensionsDataSourcePoller.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\RemoteInboxNotifications' => $baseDir . '/src/Internal/Admin/RemoteInboxNotifications.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Schedulers\\CustomersScheduler' => $baseDir . '/src/Internal/Admin/Schedulers/CustomersScheduler.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Schedulers\\ImportInterface' => $baseDir . '/src/Internal/Admin/Schedulers/ImportInterface.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Schedulers\\ImportScheduler' => $baseDir . '/src/Internal/Admin/Schedulers/ImportScheduler.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Schedulers\\MailchimpScheduler' => $baseDir . '/src/Internal/Admin/Schedulers/MailchimpScheduler.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Schedulers\\OrdersScheduler' => $baseDir . '/src/Internal/Admin/Schedulers/OrdersScheduler.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Settings' => $baseDir . '/src/Internal/Admin/Settings.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\Exceptions\\ApiArgumentException' => $baseDir . '/src/Internal/Admin/Settings/Exceptions/ApiArgumentException.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\Exceptions\\ApiException' => $baseDir . '/src/Internal/Admin/Settings/Exceptions/ApiException.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\PaymentProviders' => $baseDir . '/src/Internal/Admin/Settings/PaymentProviders.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\PaymentProviders\\AmazonPay' => $baseDir . '/src/Internal/Admin/Settings/PaymentProviders/AmazonPay.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\PaymentProviders\\MercadoPago' => $baseDir . '/src/Internal/Admin/Settings/PaymentProviders/MercadoPago.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\PaymentProviders\\Mollie' => $baseDir . '/src/Internal/Admin/Settings/PaymentProviders/Mollie.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\PaymentProviders\\PayPal' => $baseDir . '/src/Internal/Admin/Settings/PaymentProviders/PayPal.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\PaymentProviders\\PaymentGateway' => $baseDir . '/src/Internal/Admin/Settings/PaymentProviders/PaymentGateway.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\PaymentProviders\\PseudoWCPaymentGateway' => $baseDir . '/src/Internal/Admin/Settings/PaymentProviders/PseudoWCPaymentGateway.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\PaymentProviders\\Stripe' => $baseDir . '/src/Internal/Admin/Settings/PaymentProviders/Stripe.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\PaymentProviders\\WCCore' => $baseDir . '/src/Internal/Admin/Settings/PaymentProviders/WCCore.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\PaymentProviders\\WooPayments' => $baseDir . '/src/Internal/Admin/Settings/PaymentProviders/WooPayments.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\PaymentProviders\\WooPayments\\WooPaymentsRestController' => $baseDir . '/src/Internal/Admin/Settings/PaymentProviders/WooPayments/WooPaymentsRestController.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\PaymentProviders\\WooPayments\\WooPaymentsService' => $baseDir . '/src/Internal/Admin/Settings/PaymentProviders/WooPayments/WooPaymentsService.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\Payments' => $baseDir . '/src/Internal/Admin/Settings/Payments.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\PaymentsController' => $baseDir . '/src/Internal/Admin/Settings/PaymentsController.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\PaymentsRestController' => $baseDir . '/src/Internal/Admin/Settings/PaymentsRestController.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\Utils' => $baseDir . '/src/Internal/Admin/Settings/Utils.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\ShippingLabelBanner' => $baseDir . '/src/Internal/Admin/ShippingLabelBanner.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\ShippingLabelBannerDisplayRules' => $baseDir . '/src/Internal/Admin/ShippingLabelBannerDisplayRules.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\SiteHealth' => $baseDir . '/src/Internal/Admin/SiteHealth.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Suggestions\\Incentives\\Incentive' => $baseDir . '/src/Internal/Admin/Suggestions/Incentives/Incentive.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Suggestions\\Incentives\\WooPayments' => $baseDir . '/src/Internal/Admin/Suggestions/Incentives/WooPayments.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Suggestions\\PaymentExtensionSuggestionIncentives' => $baseDir . '/src/Internal/Admin/Suggestions/PaymentExtensionSuggestionIncentives.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Suggestions\\PaymentExtensionSuggestions' => $baseDir . '/src/Internal/Admin/Suggestions/PaymentExtensionSuggestions.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Survey' => $baseDir . '/src/Internal/Admin/Survey.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\SystemStatusReport' => $baseDir . '/src/Internal/Admin/SystemStatusReport.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\Translations' => $baseDir . '/src/Internal/Admin/Translations.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\WCAdminAssets' => $baseDir . '/src/Internal/Admin/WCAdminAssets.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\WCAdminSharedSettings' => $baseDir . '/src/Internal/Admin/WCAdminSharedSettings.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\WCAdminUser' => $baseDir . '/src/Internal/Admin/WCAdminUser.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\WCPayPromotion\\DefaultPromotions' => $baseDir . '/src/Internal/Admin/WCPayPromotion/DefaultPromotions.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\WCPayPromotion\\Init' => $baseDir . '/src/Internal/Admin/WCPayPromotion/Init.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\WCPayPromotion\\WCPayPromotionDataSourcePoller' => $baseDir . '/src/Internal/Admin/WCPayPromotion/WCPayPromotionDataSourcePoller.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\WCPayPromotion\\WCPaymentGatewayPreInstallWCPayPromotion' => $baseDir . '/src/Internal/Admin/WCPayPromotion/WCPaymentGatewayPreInstallWCPayPromotion.php',
    'Automattic\\WooCommerce\\Internal\\Admin\\WcPayWelcomePage' => $baseDir . '/src/Internal/Admin/WcPayWelcomePage.php',
    'Automattic\\WooCommerce\\Internal\\AssignDefaultCategory' => $baseDir . '/src/Internal/AssignDefaultCategory.php',
    'Automattic\\WooCommerce\\Internal\\BatchProcessing\\BatchProcessingController' => $baseDir . '/src/Internal/BatchProcessing/BatchProcessingController.php',
    'Automattic\\WooCommerce\\Internal\\BatchProcessing\\BatchProcessorInterface' => $baseDir . '/src/Internal/BatchProcessing/BatchProcessorInterface.php',
    'Automattic\\WooCommerce\\Internal\\Brands' => $baseDir . '/src/Internal/Brands.php',
    'Automattic\\WooCommerce\\Internal\\ComingSoon\\ComingSoonAdminBarBadge' => $baseDir . '/src/Internal/ComingSoon/ComingSoonAdminBarBadge.php',
    'Automattic\\WooCommerce\\Internal\\ComingSoon\\ComingSoonCacheInvalidator' => $baseDir . '/src/Internal/ComingSoon/ComingSoonCacheInvalidator.php',
    'Automattic\\WooCommerce\\Internal\\ComingSoon\\ComingSoonHelper' => $baseDir . '/src/Internal/ComingSoon/ComingSoonHelper.php',
    'Automattic\\WooCommerce\\Internal\\ComingSoon\\ComingSoonRequestHandler' => $baseDir . '/src/Internal/ComingSoon/ComingSoonRequestHandler.php',
    'Automattic\\WooCommerce\\Internal\\CostOfGoodsSold\\CogsAwareRestControllerTrait' => $baseDir . '/src/Internal/CostOfGoodsSold/CogsAwareRestControllerTrait.php',
    'Automattic\\WooCommerce\\Internal\\CostOfGoodsSold\\CogsAwareTrait' => $baseDir . '/src/Internal/CostOfGoodsSold/CogsAwareTrait.php',
    'Automattic\\WooCommerce\\Internal\\CostOfGoodsSold\\CogsAwareUnitTestSuiteTrait' => $baseDir . '/src/Internal/CostOfGoodsSold/CogsAwareUnitTestSuiteTrait.php',
    'Automattic\\WooCommerce\\Internal\\CostOfGoodsSold\\CostOfGoodsSoldController' => $baseDir . '/src/Internal/CostOfGoodsSold/CostOfGoodsSoldController.php',
    'Automattic\\WooCommerce\\Internal\\DataStores\\CustomMetaDataStore' => $baseDir . '/src/Internal/DataStores/CustomMetaDataStore.php',
    'Automattic\\WooCommerce\\Internal\\DataStores\\Orders\\CustomOrdersTableController' => $baseDir . '/src/Internal/DataStores/Orders/CustomOrdersTableController.php',
    'Automattic\\WooCommerce\\Internal\\DataStores\\Orders\\DataSynchronizer' => $baseDir . '/src/Internal/DataStores/Orders/DataSynchronizer.php',
    'Automattic\\WooCommerce\\Internal\\DataStores\\Orders\\LegacyDataCleanup' => $baseDir . '/src/Internal/DataStores/Orders/LegacyDataCleanup.php',
    'Automattic\\WooCommerce\\Internal\\DataStores\\Orders\\LegacyDataHandler' => $baseDir . '/src/Internal/DataStores/Orders/LegacyDataHandler.php',
    'Automattic\\WooCommerce\\Internal\\DataStores\\Orders\\OrdersTableDataStore' => $baseDir . '/src/Internal/DataStores/Orders/OrdersTableDataStore.php',
    'Automattic\\WooCommerce\\Internal\\DataStores\\Orders\\OrdersTableDataStoreMeta' => $baseDir . '/src/Internal/DataStores/Orders/OrdersTableDataStoreMeta.php',
    'Automattic\\WooCommerce\\Internal\\DataStores\\Orders\\OrdersTableFieldQuery' => $baseDir . '/src/Internal/DataStores/Orders/OrdersTableFieldQuery.php',
    'Automattic\\WooCommerce\\Internal\\DataStores\\Orders\\OrdersTableMetaQuery' => $baseDir . '/src/Internal/DataStores/Orders/OrdersTableMetaQuery.php',
    'Automattic\\WooCommerce\\Internal\\DataStores\\Orders\\OrdersTableQuery' => $baseDir . '/src/Internal/DataStores/Orders/OrdersTableQuery.php',
    'Automattic\\WooCommerce\\Internal\\DataStores\\Orders\\OrdersTableRefundDataStore' => $baseDir . '/src/Internal/DataStores/Orders/OrdersTableRefundDataStore.php',
    'Automattic\\WooCommerce\\Internal\\DataStores\\Orders\\OrdersTableSearchQuery' => $baseDir . '/src/Internal/DataStores/Orders/OrdersTableSearchQuery.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\AbstractServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/AbstractServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ContainerException' => $baseDir . '/src/Internal/DependencyManagement/ContainerException.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\Definition' => $baseDir . '/src/Internal/DependencyManagement/Definition.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ExtendedContainer' => $baseDir . '/src/Internal/DependencyManagement/ExtendedContainer.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\RuntimeContainer' => $baseDir . '/src/Internal/DependencyManagement/RuntimeContainer.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\AbstractInterfaceServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/AbstractInterfaceServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\AddressProviderServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/AddressProviderServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\AdminSettingsServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/AdminSettingsServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\AdminSuggestionsServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/AdminSuggestionsServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\AssignDefaultCategoryServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/AssignDefaultCategoryServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\BatchProcessingServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/BatchProcessingServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\COTMigrationServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/COTMigrationServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\ComingSoonServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/ComingSoonServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\CostOfGoodsSoldServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/CostOfGoodsSoldServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\DownloadPermissionsAdjusterServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/DownloadPermissionsAdjusterServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\EmailEditorServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/EmailEditorServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\EmailPreviewServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/EmailPreviewServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\EnginesServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/EnginesServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\FeaturesServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/FeaturesServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\ImportExportServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/ImportExportServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\LayoutTemplatesServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/LayoutTemplatesServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\LoggingServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/LoggingServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\MarketingServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/MarketingServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\MarketplaceServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/MarketplaceServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\ObjectCacheServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/ObjectCacheServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\OptionSanitizerServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/OptionSanitizerServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\OrderAdminServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/OrderAdminServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\OrderAttributionServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/OrderAttributionServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\OrderMetaBoxServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/OrderMetaBoxServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\OrdersControllersServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/OrdersControllersServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\OrdersDataStoreServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/OrdersDataStoreServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\ProductAttributesLookupServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/ProductAttributesLookupServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\ProductDownloadsServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/ProductDownloadsServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\ProductFiltersServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/ProductFiltersServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\ProductImageBySKUServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/ProductImageBySKUServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\ProductReviewsServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/ProductReviewsServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\ProxiesServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/ProxiesServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\RestockRefundedItemsAdjusterServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/RestockRefundedItemsAdjusterServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\StatsServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/StatsServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\UtilsClassesServiceProvider' => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/UtilsClassesServiceProvider.php',
    'Automattic\\WooCommerce\\Internal\\DownloadPermissionsAdjuster' => $baseDir . '/src/Internal/DownloadPermissionsAdjuster.php',
    'Automattic\\WooCommerce\\Internal\\EmailEditor\\BlockEmailRenderer' => $baseDir . '/src/Internal/EmailEditor/BlockEmailRenderer.php',
    'Automattic\\WooCommerce\\Internal\\EmailEditor\\EmailApiController' => $baseDir . '/src/Internal/EmailEditor/EmailApiController.php',
    'Automattic\\WooCommerce\\Internal\\EmailEditor\\EmailPatterns\\PatternsController' => $baseDir . '/src/Internal/EmailEditor/EmailPatterns/PatternsController.php',
    'Automattic\\WooCommerce\\Internal\\EmailEditor\\EmailPatterns\\WooEmailContentPattern' => $baseDir . '/src/Internal/EmailEditor/EmailPatterns/WooEmailContentPattern.php',
    'Automattic\\WooCommerce\\Internal\\EmailEditor\\EmailTemplates\\TemplateApiController' => $baseDir . '/src/Internal/EmailEditor/EmailTemplates/TemplateApiController.php',
    'Automattic\\WooCommerce\\Internal\\EmailEditor\\EmailTemplates\\TemplatesController' => $baseDir . '/src/Internal/EmailEditor/EmailTemplates/TemplatesController.php',
    'Automattic\\WooCommerce\\Internal\\EmailEditor\\EmailTemplates\\WooEmailTemplate' => $baseDir . '/src/Internal/EmailEditor/EmailTemplates/WooEmailTemplate.php',
    'Automattic\\WooCommerce\\Internal\\EmailEditor\\Integration' => $baseDir . '/src/Internal/EmailEditor/Integration.php',
    'Automattic\\WooCommerce\\Internal\\EmailEditor\\Package' => $baseDir . '/src/Internal/EmailEditor/Package.php',
    'Automattic\\WooCommerce\\Internal\\EmailEditor\\PageRenderer' => $baseDir . '/src/Internal/EmailEditor/PageRenderer.php',
    'Automattic\\WooCommerce\\Internal\\EmailEditor\\PersonalizationTagManager' => $baseDir . '/src/Internal/EmailEditor/PersonalizationTagManager.php',
    'Automattic\\WooCommerce\\Internal\\EmailEditor\\PersonalizationTags\\AbstractTagProvider' => $baseDir . '/src/Internal/EmailEditor/PersonalizationTags/AbstractTagProvider.php',
    'Automattic\\WooCommerce\\Internal\\EmailEditor\\PersonalizationTags\\CustomerTagsProvider' => $baseDir . '/src/Internal/EmailEditor/PersonalizationTags/CustomerTagsProvider.php',
    'Automattic\\WooCommerce\\Internal\\EmailEditor\\PersonalizationTags\\OrderTagsProvider' => $baseDir . '/src/Internal/EmailEditor/PersonalizationTags/OrderTagsProvider.php',
    'Automattic\\WooCommerce\\Internal\\EmailEditor\\PersonalizationTags\\SiteTagsProvider' => $baseDir . '/src/Internal/EmailEditor/PersonalizationTags/SiteTagsProvider.php',
    'Automattic\\WooCommerce\\Internal\\EmailEditor\\PersonalizationTags\\StoreTagsProvider' => $baseDir . '/src/Internal/EmailEditor/PersonalizationTags/StoreTagsProvider.php',
    'Automattic\\WooCommerce\\Internal\\EmailEditor\\Renderer\\Blocks\\WooContent' => $baseDir . '/src/Internal/EmailEditor/Renderer/Blocks/WooContent.php',
    'Automattic\\WooCommerce\\Internal\\EmailEditor\\TransactionalEmailPersonalizer' => $baseDir . '/src/Internal/EmailEditor/TransactionalEmailPersonalizer.php',
    'Automattic\\WooCommerce\\Internal\\EmailEditor\\WCTransactionalEmails\\WCTransactionalEmailPostsGenerator' => $baseDir . '/src/Internal/EmailEditor/WCTransactionalEmails/WCTransactionalEmailPostsGenerator.php',
    'Automattic\\WooCommerce\\Internal\\EmailEditor\\WCTransactionalEmails\\WCTransactionalEmailPostsManager' => $baseDir . '/src/Internal/EmailEditor/WCTransactionalEmails/WCTransactionalEmailPostsManager.php',
    'Automattic\\WooCommerce\\Internal\\EmailEditor\\WCTransactionalEmails\\WCTransactionalEmails' => $baseDir . '/src/Internal/EmailEditor/WCTransactionalEmails/WCTransactionalEmails.php',
    'Automattic\\WooCommerce\\Internal\\EmailEditor\\WooContentProcessor' => $baseDir . '/src/Internal/EmailEditor/WooContentProcessor.php',
    'Automattic\\WooCommerce\\Internal\\Email\\EmailColors' => $baseDir . '/src/Internal/Email/EmailColors.php',
    'Automattic\\WooCommerce\\Internal\\Email\\EmailFont' => $baseDir . '/src/Internal/Email/EmailFont.php',
    'Automattic\\WooCommerce\\Internal\\Email\\EmailStyleSync' => $baseDir . '/src/Internal/Email/EmailStyleSync.php',
    'Automattic\\WooCommerce\\Internal\\Email\\OrderPriceFormatter' => $baseDir . '/src/Internal/Email/OrderPriceFormatter.php',
    'Automattic\\WooCommerce\\Internal\\Features\\FeaturesController' => $baseDir . '/src/Internal/Features/FeaturesController.php',
    'Automattic\\WooCommerce\\Internal\\Features\\ProductBlockEditor\\ProductTemplates\\AbstractProductFormTemplate' => $baseDir . '/src/Internal/Features/ProductBlockEditor/ProductTemplates/AbstractProductFormTemplate.php',
    'Automattic\\WooCommerce\\Internal\\Features\\ProductBlockEditor\\ProductTemplates\\DownloadableProductTrait' => $baseDir . '/src/Internal/Features/ProductBlockEditor/ProductTemplates/DownloadableProductTrait.php',
    'Automattic\\WooCommerce\\Internal\\Features\\ProductBlockEditor\\ProductTemplates\\Group' => $baseDir . '/src/Internal/Features/ProductBlockEditor/ProductTemplates/Group.php',
    'Automattic\\WooCommerce\\Internal\\Features\\ProductBlockEditor\\ProductTemplates\\ProductBlock' => $baseDir . '/src/Internal/Features/ProductBlockEditor/ProductTemplates/ProductBlock.php',
    'Automattic\\WooCommerce\\Internal\\Features\\ProductBlockEditor\\ProductTemplates\\ProductVariationTemplate' => $baseDir . '/src/Internal/Features/ProductBlockEditor/ProductTemplates/ProductVariationTemplate.php',
    'Automattic\\WooCommerce\\Internal\\Features\\ProductBlockEditor\\ProductTemplates\\Section' => $baseDir . '/src/Internal/Features/ProductBlockEditor/ProductTemplates/Section.php',
    'Automattic\\WooCommerce\\Internal\\Features\\ProductBlockEditor\\ProductTemplates\\SimpleProductTemplate' => $baseDir . '/src/Internal/Features/ProductBlockEditor/ProductTemplates/SimpleProductTemplate.php',
    'Automattic\\WooCommerce\\Internal\\Features\\ProductBlockEditor\\ProductTemplates\\Subsection' => $baseDir . '/src/Internal/Features/ProductBlockEditor/ProductTemplates/Subsection.php',
    'Automattic\\WooCommerce\\Internal\\Font\\FontFace' => $baseDir . '/src/Internal/Font/FontFace.php',
    'Automattic\\WooCommerce\\Internal\\Font\\FontFamily' => $baseDir . '/src/Internal/Font/FontFamily.php',
    'Automattic\\WooCommerce\\Internal\\Integrations\\WPConsentAPI' => $baseDir . '/src/Internal/Integrations/WPConsentAPI.php',
    'Automattic\\WooCommerce\\Internal\\Logging\\RemoteLogger' => $baseDir . '/src/Internal/Logging/RemoteLogger.php',
    'Automattic\\WooCommerce\\Internal\\Logging\\SafeGlobalFunctionProxy' => $baseDir . '/src/Internal/Logging/SafeGlobalFunctionProxy.php',
    'Automattic\\WooCommerce\\Internal\\McStats' => $baseDir . '/src/Internal/McStats.php',
    'Automattic\\WooCommerce\\Internal\\OrderCouponDataMigrator' => $baseDir . '/src/Internal/OrderCouponDataMigrator.php',
    'Automattic\\WooCommerce\\Internal\\Orders\\CouponsController' => $baseDir . '/src/Internal/Orders/CouponsController.php',
    'Automattic\\WooCommerce\\Internal\\Orders\\IppFunctions' => $baseDir . '/src/Internal/Orders/IppFunctions.php',
    'Automattic\\WooCommerce\\Internal\\Orders\\MobileMessagingHandler' => $baseDir . '/src/Internal/Orders/MobileMessagingHandler.php',
    'Automattic\\WooCommerce\\Internal\\Orders\\OrderActionsRestController' => $baseDir . '/src/Internal/Orders/OrderActionsRestController.php',
    'Automattic\\WooCommerce\\Internal\\Orders\\OrderAttributionBlocksController' => $baseDir . '/src/Internal/Orders/OrderAttributionBlocksController.php',
    'Automattic\\WooCommerce\\Internal\\Orders\\OrderAttributionController' => $baseDir . '/src/Internal/Orders/OrderAttributionController.php',
    'Automattic\\WooCommerce\\Internal\\Orders\\OrderStatusRestController' => $baseDir . '/src/Internal/Orders/OrderStatusRestController.php',
    'Automattic\\WooCommerce\\Internal\\Orders\\PaymentInfo' => $baseDir . '/src/Internal/Orders/PaymentInfo.php',
    'Automattic\\WooCommerce\\Internal\\Orders\\PointOfSaleOrderUtil' => $baseDir . '/src/Internal/Orders/PointOfSaleOrderUtil.php',
    'Automattic\\WooCommerce\\Internal\\Orders\\TaxesController' => $baseDir . '/src/Internal/Orders/TaxesController.php',
    'Automattic\\WooCommerce\\Internal\\ProductAttributesLookup\\CLIRunner' => $baseDir . '/src/Internal/ProductAttributesLookup/CLIRunner.php',
    'Automattic\\WooCommerce\\Internal\\ProductAttributesLookup\\DataRegenerator' => $baseDir . '/src/Internal/ProductAttributesLookup/DataRegenerator.php',
    'Automattic\\WooCommerce\\Internal\\ProductAttributesLookup\\Filterer' => $baseDir . '/src/Internal/ProductAttributesLookup/Filterer.php',
    'Automattic\\WooCommerce\\Internal\\ProductAttributesLookup\\LookupDataStore' => $baseDir . '/src/Internal/ProductAttributesLookup/LookupDataStore.php',
    'Automattic\\WooCommerce\\Internal\\ProductDownloads\\ApprovedDirectories\\Admin\\SyncUI' => $baseDir . '/src/Internal/ProductDownloads/ApprovedDirectories/Admin/SyncUI.php',
    'Automattic\\WooCommerce\\Internal\\ProductDownloads\\ApprovedDirectories\\Admin\\Table' => $baseDir . '/src/Internal/ProductDownloads/ApprovedDirectories/Admin/Table.php',
    'Automattic\\WooCommerce\\Internal\\ProductDownloads\\ApprovedDirectories\\Admin\\UI' => $baseDir . '/src/Internal/ProductDownloads/ApprovedDirectories/Admin/UI.php',
    'Automattic\\WooCommerce\\Internal\\ProductDownloads\\ApprovedDirectories\\ApprovedDirectoriesException' => $baseDir . '/src/Internal/ProductDownloads/ApprovedDirectories/ApprovedDirectoriesException.php',
    'Automattic\\WooCommerce\\Internal\\ProductDownloads\\ApprovedDirectories\\Register' => $baseDir . '/src/Internal/ProductDownloads/ApprovedDirectories/Register.php',
    'Automattic\\WooCommerce\\Internal\\ProductDownloads\\ApprovedDirectories\\StoredUrl' => $baseDir . '/src/Internal/ProductDownloads/ApprovedDirectories/StoredUrl.php',
    'Automattic\\WooCommerce\\Internal\\ProductDownloads\\ApprovedDirectories\\Synchronize' => $baseDir . '/src/Internal/ProductDownloads/ApprovedDirectories/Synchronize.php',
    'Automattic\\WooCommerce\\Internal\\ProductFilters\\CacheController' => $baseDir . '/src/Internal/ProductFilters/CacheController.php',
    'Automattic\\WooCommerce\\Internal\\ProductFilters\\FilterData' => $baseDir . '/src/Internal/ProductFilters/FilterData.php',
    'Automattic\\WooCommerce\\Internal\\ProductFilters\\FilterDataProvider' => $baseDir . '/src/Internal/ProductFilters/FilterDataProvider.php',
    'Automattic\\WooCommerce\\Internal\\ProductFilters\\Interfaces\\QueryClausesGenerator' => $baseDir . '/src/Internal/ProductFilters/Interfaces/QueryClausesGenerator.php',
    'Automattic\\WooCommerce\\Internal\\ProductFilters\\MainQueryController' => $baseDir . '/src/Internal/ProductFilters/MainQueryController.php',
    'Automattic\\WooCommerce\\Internal\\ProductFilters\\QueryClauses' => $baseDir . '/src/Internal/ProductFilters/QueryClauses.php',
    'Automattic\\WooCommerce\\Internal\\ProductImage\\MatchImageBySKU' => $baseDir . '/src/Internal/ProductImage/MatchImageBySKU.php',
    'Automattic\\WooCommerce\\Internal\\ReceiptRendering\\ReceiptRenderingEngine' => $baseDir . '/src/Internal/ReceiptRendering/ReceiptRenderingEngine.php',
    'Automattic\\WooCommerce\\Internal\\ReceiptRendering\\ReceiptRenderingRestController' => $baseDir . '/src/Internal/ReceiptRendering/ReceiptRenderingRestController.php',
    'Automattic\\WooCommerce\\Internal\\RegisterHooksInterface' => $baseDir . '/src/Internal/RegisterHooksInterface.php',
    'Automattic\\WooCommerce\\Internal\\RestApiControllerBase' => $baseDir . '/src/Internal/RestApiControllerBase.php',
    'Automattic\\WooCommerce\\Internal\\RestApiParameterUtil' => $baseDir . '/src/Internal/RestApiParameterUtil.php',
    'Automattic\\WooCommerce\\Internal\\RestockRefundedItemsAdjuster' => $baseDir . '/src/Internal/RestockRefundedItemsAdjuster.php',
    'Automattic\\WooCommerce\\Internal\\Settings\\OptionSanitizer' => $baseDir . '/src/Internal/Settings/OptionSanitizer.php',
    'Automattic\\WooCommerce\\Internal\\Traits\\AccessiblePrivateMethods' => $baseDir . '/src/Internal/Traits/AccessiblePrivateMethods.php',
    'Automattic\\WooCommerce\\Internal\\Traits\\OrderAttributionMeta' => $baseDir . '/src/Internal/Traits/OrderAttributionMeta.php',
    'Automattic\\WooCommerce\\Internal\\Traits\\ScriptDebug' => $baseDir . '/src/Internal/Traits/ScriptDebug.php',
    'Automattic\\WooCommerce\\Internal\\TransientFiles\\TransientFilesEngine' => $baseDir . '/src/Internal/TransientFiles/TransientFilesEngine.php',
    'Automattic\\WooCommerce\\Internal\\Utilities\\ArrayUtil' => $baseDir . '/src/Internal/Utilities/ArrayUtil.php',
    'Automattic\\WooCommerce\\Internal\\Utilities\\BlocksUtil' => $baseDir . '/src/Internal/Utilities/BlocksUtil.php',
    'Automattic\\WooCommerce\\Internal\\Utilities\\COTMigrationUtil' => $baseDir . '/src/Internal/Utilities/COTMigrationUtil.php',
    'Automattic\\WooCommerce\\Internal\\Utilities\\DatabaseUtil' => $baseDir . '/src/Internal/Utilities/DatabaseUtil.php',
    'Automattic\\WooCommerce\\Internal\\Utilities\\FilesystemUtil' => $baseDir . '/src/Internal/Utilities/FilesystemUtil.php',
    'Automattic\\WooCommerce\\Internal\\Utilities\\HtmlSanitizer' => $baseDir . '/src/Internal/Utilities/HtmlSanitizer.php',
    'Automattic\\WooCommerce\\Internal\\Utilities\\LegacyRestApiStub' => $baseDir . '/src/Internal/Utilities/LegacyRestApiStub.php',
    'Automattic\\WooCommerce\\Internal\\Utilities\\PluginInstaller' => $baseDir . '/src/Internal/Utilities/PluginInstaller.php',
    'Automattic\\WooCommerce\\Internal\\Utilities\\Types' => $baseDir . '/src/Internal/Utilities/Types.php',
    'Automattic\\WooCommerce\\Internal\\Utilities\\URL' => $baseDir . '/src/Internal/Utilities/URL.php',
    'Automattic\\WooCommerce\\Internal\\Utilities\\URLException' => $baseDir . '/src/Internal/Utilities/URLException.php',
    'Automattic\\WooCommerce\\Internal\\Utilities\\Users' => $baseDir . '/src/Internal/Utilities/Users.php',
    'Automattic\\WooCommerce\\Internal\\Utilities\\WebhookUtil' => $baseDir . '/src/Internal/Utilities/WebhookUtil.php',
    'Automattic\\WooCommerce\\Internal\\WCCom\\ConnectionHelper' => $baseDir . '/src/Internal/WCCom/ConnectionHelper.php',
    'Automattic\\WooCommerce\\LayoutTemplates\\LayoutTemplateRegistry' => $baseDir . '/src/LayoutTemplates/LayoutTemplateRegistry.php',
    'Automattic\\WooCommerce\\Packages' => $baseDir . '/src/Packages.php',
    'Automattic\\WooCommerce\\Proxies\\ActionsProxy' => $baseDir . '/src/Proxies/ActionsProxy.php',
    'Automattic\\WooCommerce\\Proxies\\LegacyProxy' => $baseDir . '/src/Proxies/LegacyProxy.php',
    'Automattic\\WooCommerce\\RestApi\\Package' => $baseDir . '/includes/rest-api/Package.php',
    'Automattic\\WooCommerce\\RestApi\\Server' => $baseDir . '/includes/rest-api/Server.php',
    'Automattic\\WooCommerce\\RestApi\\Utilities\\ImageAttachment' => $baseDir . '/includes/rest-api/Utilities/ImageAttachment.php',
    'Automattic\\WooCommerce\\RestApi\\Utilities\\SingletonTrait' => $baseDir . '/includes/rest-api/Utilities/SingletonTrait.php',
    'Automattic\\WooCommerce\\StoreApi\\Authentication' => $baseDir . '/src/StoreApi/Authentication.php',
    'Automattic\\WooCommerce\\StoreApi\\Exceptions\\InvalidCartException' => $baseDir . '/src/StoreApi/Exceptions/InvalidCartException.php',
    'Automattic\\WooCommerce\\StoreApi\\Exceptions\\InvalidStockLevelsInCartException' => $baseDir . '/src/StoreApi/Exceptions/InvalidStockLevelsInCartException.php',
    'Automattic\\WooCommerce\\StoreApi\\Exceptions\\NotPurchasableException' => $baseDir . '/src/StoreApi/Exceptions/NotPurchasableException.php',
    'Automattic\\WooCommerce\\StoreApi\\Exceptions\\OutOfStockException' => $baseDir . '/src/StoreApi/Exceptions/OutOfStockException.php',
    'Automattic\\WooCommerce\\StoreApi\\Exceptions\\PartialOutOfStockException' => $baseDir . '/src/StoreApi/Exceptions/PartialOutOfStockException.php',
    'Automattic\\WooCommerce\\StoreApi\\Exceptions\\RouteException' => $baseDir . '/src/StoreApi/Exceptions/RouteException.php',
    'Automattic\\WooCommerce\\StoreApi\\Exceptions\\StockAvailabilityException' => $baseDir . '/src/StoreApi/Exceptions/StockAvailabilityException.php',
    'Automattic\\WooCommerce\\StoreApi\\Exceptions\\TooManyInCartException' => $baseDir . '/src/StoreApi/Exceptions/TooManyInCartException.php',
    'Automattic\\WooCommerce\\StoreApi\\Formatters' => $baseDir . '/src/StoreApi/Formatters.php',
    'Automattic\\WooCommerce\\StoreApi\\Formatters\\CurrencyFormatter' => $baseDir . '/src/StoreApi/Formatters/CurrencyFormatter.php',
    'Automattic\\WooCommerce\\StoreApi\\Formatters\\DefaultFormatter' => $baseDir . '/src/StoreApi/Formatters/DefaultFormatter.php',
    'Automattic\\WooCommerce\\StoreApi\\Formatters\\FormatterInterface' => $baseDir . '/src/StoreApi/Formatters/FormatterInterface.php',
    'Automattic\\WooCommerce\\StoreApi\\Formatters\\HtmlFormatter' => $baseDir . '/src/StoreApi/Formatters/HtmlFormatter.php',
    'Automattic\\WooCommerce\\StoreApi\\Formatters\\MoneyFormatter' => $baseDir . '/src/StoreApi/Formatters/MoneyFormatter.php',
    'Automattic\\WooCommerce\\StoreApi\\Legacy' => $baseDir . '/src/StoreApi/Legacy.php',
    'Automattic\\WooCommerce\\StoreApi\\Payments\\PaymentContext' => $baseDir . '/src/StoreApi/Payments/PaymentContext.php',
    'Automattic\\WooCommerce\\StoreApi\\Payments\\PaymentResult' => $baseDir . '/src/StoreApi/Payments/PaymentResult.php',
    'Automattic\\WooCommerce\\StoreApi\\RoutesController' => $baseDir . '/src/StoreApi/RoutesController.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\RouteInterface' => $baseDir . '/src/StoreApi/Routes/RouteInterface.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\AI\\Middleware' => $baseDir . '/src/StoreApi/Routes/V1/AI/Middleware.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\AI\\Products' => $baseDir . '/src/StoreApi/Routes/V1/AI/Products.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\AbstractCartRoute' => $baseDir . '/src/StoreApi/Routes/V1/AbstractCartRoute.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\AbstractRoute' => $baseDir . '/src/StoreApi/Routes/V1/AbstractRoute.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\AbstractTermsRoute' => $baseDir . '/src/StoreApi/Routes/V1/AbstractTermsRoute.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\Batch' => $baseDir . '/src/StoreApi/Routes/V1/Batch.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\Cart' => $baseDir . '/src/StoreApi/Routes/V1/Cart.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\CartAddItem' => $baseDir . '/src/StoreApi/Routes/V1/CartAddItem.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\CartApplyCoupon' => $baseDir . '/src/StoreApi/Routes/V1/CartApplyCoupon.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\CartCoupons' => $baseDir . '/src/StoreApi/Routes/V1/CartCoupons.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\CartCouponsByCode' => $baseDir . '/src/StoreApi/Routes/V1/CartCouponsByCode.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\CartExtensions' => $baseDir . '/src/StoreApi/Routes/V1/CartExtensions.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\CartItems' => $baseDir . '/src/StoreApi/Routes/V1/CartItems.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\CartItemsByKey' => $baseDir . '/src/StoreApi/Routes/V1/CartItemsByKey.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\CartRemoveCoupon' => $baseDir . '/src/StoreApi/Routes/V1/CartRemoveCoupon.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\CartRemoveItem' => $baseDir . '/src/StoreApi/Routes/V1/CartRemoveItem.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\CartSelectShippingRate' => $baseDir . '/src/StoreApi/Routes/V1/CartSelectShippingRate.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\CartUpdateCustomer' => $baseDir . '/src/StoreApi/Routes/V1/CartUpdateCustomer.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\CartUpdateItem' => $baseDir . '/src/StoreApi/Routes/V1/CartUpdateItem.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\Checkout' => $baseDir . '/src/StoreApi/Routes/V1/Checkout.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\CheckoutOrder' => $baseDir . '/src/StoreApi/Routes/V1/CheckoutOrder.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\Order' => $baseDir . '/src/StoreApi/Routes/V1/Order.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\Patterns' => $baseDir . '/src/StoreApi/Routes/V1/Patterns.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\ProductAttributeTerms' => $baseDir . '/src/StoreApi/Routes/V1/ProductAttributeTerms.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\ProductAttributes' => $baseDir . '/src/StoreApi/Routes/V1/ProductAttributes.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\ProductAttributesById' => $baseDir . '/src/StoreApi/Routes/V1/ProductAttributesById.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\ProductBrands' => $baseDir . '/src/StoreApi/Routes/V1/ProductBrands.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\ProductBrandsById' => $baseDir . '/src/StoreApi/Routes/V1/ProductBrandsById.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\ProductCategories' => $baseDir . '/src/StoreApi/Routes/V1/ProductCategories.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\ProductCategoriesById' => $baseDir . '/src/StoreApi/Routes/V1/ProductCategoriesById.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\ProductCollectionData' => $baseDir . '/src/StoreApi/Routes/V1/ProductCollectionData.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\ProductReviews' => $baseDir . '/src/StoreApi/Routes/V1/ProductReviews.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\ProductTags' => $baseDir . '/src/StoreApi/Routes/V1/ProductTags.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\Products' => $baseDir . '/src/StoreApi/Routes/V1/Products.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\ProductsById' => $baseDir . '/src/StoreApi/Routes/V1/ProductsById.php',
    'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\ProductsBySlug' => $baseDir . '/src/StoreApi/Routes/V1/ProductsBySlug.php',
    'Automattic\\WooCommerce\\StoreApi\\SchemaController' => $baseDir . '/src/StoreApi/SchemaController.php',
    'Automattic\\WooCommerce\\StoreApi\\Schemas\\ExtendSchema' => $baseDir . '/src/StoreApi/Schemas/ExtendSchema.php',
    'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\AI\\ProductsSchema' => $baseDir . '/src/StoreApi/Schemas/V1/AI/ProductsSchema.php',
    'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\AbstractAddressSchema' => $baseDir . '/src/StoreApi/Schemas/V1/AbstractAddressSchema.php',
    'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\AbstractSchema' => $baseDir . '/src/StoreApi/Schemas/V1/AbstractSchema.php',
    'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\BatchSchema' => $baseDir . '/src/StoreApi/Schemas/V1/BatchSchema.php',
    'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\BillingAddressSchema' => $baseDir . '/src/StoreApi/Schemas/V1/BillingAddressSchema.php',
    'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\CartCouponSchema' => $baseDir . '/src/StoreApi/Schemas/V1/CartCouponSchema.php',
    'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\CartExtensionsSchema' => $baseDir . '/src/StoreApi/Schemas/V1/CartExtensionsSchema.php',
    'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\CartFeeSchema' => $baseDir . '/src/StoreApi/Schemas/V1/CartFeeSchema.php',
    'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\CartItemSchema' => $baseDir . '/src/StoreApi/Schemas/V1/CartItemSchema.php',
    'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\CartSchema' => $baseDir . '/src/StoreApi/Schemas/V1/CartSchema.php',
    'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\CartShippingRateSchema' => $baseDir . '/src/StoreApi/Schemas/V1/CartShippingRateSchema.php',
    'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\CheckoutOrderSchema' => $baseDir . '/src/StoreApi/Schemas/V1/CheckoutOrderSchema.php',
    'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\CheckoutSchema' => $baseDir . '/src/StoreApi/Schemas/V1/CheckoutSchema.php',
    'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\ErrorSchema' => $baseDir . '/src/StoreApi/Schemas/V1/ErrorSchema.php',
    'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\ImageAttachmentSchema' => $baseDir . '/src/StoreApi/Schemas/V1/ImageAttachmentSchema.php',
    'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\ItemSchema' => $baseDir . '/src/StoreApi/Schemas/V1/ItemSchema.php',
    'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\OrderCouponSchema' => $baseDir . '/src/StoreApi/Schemas/V1/OrderCouponSchema.php',
    'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\OrderFeeSchema' => $baseDir . '/src/StoreApi/Schemas/V1/OrderFeeSchema.php',
    'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\OrderItemSchema' => $baseDir . '/src/StoreApi/Schemas/V1/OrderItemSchema.php',
    'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\OrderSchema' => $baseDir . '/src/StoreApi/Schemas/V1/OrderSchema.php',
    'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\PatternsSchema' => $baseDir . '/src/StoreApi/Schemas/V1/PatternsSchema.php',
    'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\ProductAttributeSchema' => $baseDir . '/src/StoreApi/Schemas/V1/ProductAttributeSchema.php',
    'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\ProductBrandSchema' => $baseDir . '/src/StoreApi/Schemas/V1/ProductBrandSchema.php',
    'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\ProductCategorySchema' => $baseDir . '/src/StoreApi/Schemas/V1/ProductCategorySchema.php',
    'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\ProductCollectionDataSchema' => $baseDir . '/src/StoreApi/Schemas/V1/ProductCollectionDataSchema.php',
    'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\ProductReviewSchema' => $baseDir . '/src/StoreApi/Schemas/V1/ProductReviewSchema.php',
    'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\ProductSchema' => $baseDir . '/src/StoreApi/Schemas/V1/ProductSchema.php',
    'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\ShippingAddressSchema' => $baseDir . '/src/StoreApi/Schemas/V1/ShippingAddressSchema.php',
    'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\TermSchema' => $baseDir . '/src/StoreApi/Schemas/V1/TermSchema.php',
    'Automattic\\WooCommerce\\StoreApi\\SessionHandler' => $baseDir . '/src/StoreApi/SessionHandler.php',
    'Automattic\\WooCommerce\\StoreApi\\StoreApi' => $baseDir . '/src/StoreApi/StoreApi.php',
    'Automattic\\WooCommerce\\StoreApi\\Utilities\\ArrayUtils' => $baseDir . '/src/StoreApi/Utilities/ArrayUtils.php',
    'Automattic\\WooCommerce\\StoreApi\\Utilities\\CartController' => $baseDir . '/src/StoreApi/Utilities/CartController.php',
    'Automattic\\WooCommerce\\StoreApi\\Utilities\\CheckoutTrait' => $baseDir . '/src/StoreApi/Utilities/CheckoutTrait.php',
    'Automattic\\WooCommerce\\StoreApi\\Utilities\\DraftOrderTrait' => $baseDir . '/src/StoreApi/Utilities/DraftOrderTrait.php',
    'Automattic\\WooCommerce\\StoreApi\\Utilities\\JsonWebToken' => $baseDir . '/src/StoreApi/Utilities/JsonWebToken.php',
    'Automattic\\WooCommerce\\StoreApi\\Utilities\\LocalPickupUtils' => $baseDir . '/src/StoreApi/Utilities/LocalPickupUtils.php',
    'Automattic\\WooCommerce\\StoreApi\\Utilities\\NoticeHandler' => $baseDir . '/src/StoreApi/Utilities/NoticeHandler.php',
    'Automattic\\WooCommerce\\StoreApi\\Utilities\\OrderAuthorizationTrait' => $baseDir . '/src/StoreApi/Utilities/OrderAuthorizationTrait.php',
    'Automattic\\WooCommerce\\StoreApi\\Utilities\\OrderController' => $baseDir . '/src/StoreApi/Utilities/OrderController.php',
    'Automattic\\WooCommerce\\StoreApi\\Utilities\\Pagination' => $baseDir . '/src/StoreApi/Utilities/Pagination.php',
    'Automattic\\WooCommerce\\StoreApi\\Utilities\\PaymentUtils' => $baseDir . '/src/StoreApi/Utilities/PaymentUtils.php',
    'Automattic\\WooCommerce\\StoreApi\\Utilities\\ProductItemTrait' => $baseDir . '/src/StoreApi/Utilities/ProductItemTrait.php',
    'Automattic\\WooCommerce\\StoreApi\\Utilities\\ProductQuery' => $baseDir . '/src/StoreApi/Utilities/ProductQuery.php',
    'Automattic\\WooCommerce\\StoreApi\\Utilities\\ProductQueryFilters' => $baseDir . '/src/StoreApi/Utilities/ProductQueryFilters.php',
    'Automattic\\WooCommerce\\StoreApi\\Utilities\\QuantityLimits' => $baseDir . '/src/StoreApi/Utilities/QuantityLimits.php',
    'Automattic\\WooCommerce\\StoreApi\\Utilities\\RateLimits' => $baseDir . '/src/StoreApi/Utilities/RateLimits.php',
    'Automattic\\WooCommerce\\StoreApi\\Utilities\\SanitizationUtils' => $baseDir . '/src/StoreApi/Utilities/SanitizationUtils.php',
    'Automattic\\WooCommerce\\StoreApi\\Utilities\\ValidationUtils' => $baseDir . '/src/StoreApi/Utilities/ValidationUtils.php',
    'Automattic\\WooCommerce\\Utilities\\ArrayUtil' => $baseDir . '/src/Utilities/ArrayUtil.php',
    'Automattic\\WooCommerce\\Utilities\\DiscountsUtil' => $baseDir . '/src/Utilities/DiscountsUtil.php',
    'Automattic\\WooCommerce\\Utilities\\FeaturesUtil' => $baseDir . '/src/Utilities/FeaturesUtil.php',
    'Automattic\\WooCommerce\\Utilities\\I18nUtil' => $baseDir . '/src/Utilities/I18nUtil.php',
    'Automattic\\WooCommerce\\Utilities\\LoggingUtil' => $baseDir . '/src/Utilities/LoggingUtil.php',
    'Automattic\\WooCommerce\\Utilities\\NumberUtil' => $baseDir . '/src/Utilities/NumberUtil.php',
    'Automattic\\WooCommerce\\Utilities\\OrderUtil' => $baseDir . '/src/Utilities/OrderUtil.php',
    'Automattic\\WooCommerce\\Utilities\\PluginUtil' => $baseDir . '/src/Utilities/PluginUtil.php',
    'Automattic\\WooCommerce\\Utilities\\RestApiUtil' => $baseDir . '/src/Utilities/RestApiUtil.php',
    'Automattic\\WooCommerce\\Utilities\\ShippingUtil' => $baseDir . '/src/Utilities/ShippingUtil.php',
    'Automattic\\WooCommerce\\Utilities\\StringUtil' => $baseDir . '/src/Utilities/StringUtil.php',
    'Automattic\\WooCommerce\\Utilities\\TimeUtil' => $baseDir . '/src/Utilities/TimeUtil.php',
    'Automattic\\WooCommerce\\Vendor\\Detection\\MobileDetect' => $baseDir . '/lib/packages/Detection/MobileDetect.php',
    'Automattic\\WooCommerce\\Vendor\\League\\Container\\Argument\\ArgumentResolverInterface' => $baseDir . '/lib/packages/League/Container/Argument/ArgumentResolverInterface.php',
    'Automattic\\WooCommerce\\Vendor\\League\\Container\\Argument\\ArgumentResolverTrait' => $baseDir . '/lib/packages/League/Container/Argument/ArgumentResolverTrait.php',
    'Automattic\\WooCommerce\\Vendor\\League\\Container\\Argument\\ClassName' => $baseDir . '/lib/packages/League/Container/Argument/ClassName.php',
    'Automattic\\WooCommerce\\Vendor\\League\\Container\\Argument\\ClassNameInterface' => $baseDir . '/lib/packages/League/Container/Argument/ClassNameInterface.php',
    'Automattic\\WooCommerce\\Vendor\\League\\Container\\Argument\\ClassNameWithOptionalValue' => $baseDir . '/lib/packages/League/Container/Argument/ClassNameWithOptionalValue.php',
    'Automattic\\WooCommerce\\Vendor\\League\\Container\\Argument\\RawArgument' => $baseDir . '/lib/packages/League/Container/Argument/RawArgument.php',
    'Automattic\\WooCommerce\\Vendor\\League\\Container\\Argument\\RawArgumentInterface' => $baseDir . '/lib/packages/League/Container/Argument/RawArgumentInterface.php',
    'Automattic\\WooCommerce\\Vendor\\League\\Container\\Container' => $baseDir . '/lib/packages/League/Container/Container.php',
    'Automattic\\WooCommerce\\Vendor\\League\\Container\\ContainerAwareInterface' => $baseDir . '/lib/packages/League/Container/ContainerAwareInterface.php',
    'Automattic\\WooCommerce\\Vendor\\League\\Container\\ContainerAwareTrait' => $baseDir . '/lib/packages/League/Container/ContainerAwareTrait.php',
    'Automattic\\WooCommerce\\Vendor\\League\\Container\\Definition\\Definition' => $baseDir . '/lib/packages/League/Container/Definition/Definition.php',
    'Automattic\\WooCommerce\\Vendor\\League\\Container\\Definition\\DefinitionAggregate' => $baseDir . '/lib/packages/League/Container/Definition/DefinitionAggregate.php',
    'Automattic\\WooCommerce\\Vendor\\League\\Container\\Definition\\DefinitionAggregateInterface' => $baseDir . '/lib/packages/League/Container/Definition/DefinitionAggregateInterface.php',
    'Automattic\\WooCommerce\\Vendor\\League\\Container\\Definition\\DefinitionInterface' => $baseDir . '/lib/packages/League/Container/Definition/DefinitionInterface.php',
    'Automattic\\WooCommerce\\Vendor\\League\\Container\\Exception\\ContainerException' => $baseDir . '/lib/packages/League/Container/Exception/ContainerException.php',
    'Automattic\\WooCommerce\\Vendor\\League\\Container\\Exception\\NotFoundException' => $baseDir . '/lib/packages/League/Container/Exception/NotFoundException.php',
    'Automattic\\WooCommerce\\Vendor\\League\\Container\\Inflector\\Inflector' => $baseDir . '/lib/packages/League/Container/Inflector/Inflector.php',
    'Automattic\\WooCommerce\\Vendor\\League\\Container\\Inflector\\InflectorAggregate' => $baseDir . '/lib/packages/League/Container/Inflector/InflectorAggregate.php',
    'Automattic\\WooCommerce\\Vendor\\League\\Container\\Inflector\\InflectorAggregateInterface' => $baseDir . '/lib/packages/League/Container/Inflector/InflectorAggregateInterface.php',
    'Automattic\\WooCommerce\\Vendor\\League\\Container\\Inflector\\InflectorInterface' => $baseDir . '/lib/packages/League/Container/Inflector/InflectorInterface.php',
    'Automattic\\WooCommerce\\Vendor\\League\\Container\\ReflectionContainer' => $baseDir . '/lib/packages/League/Container/ReflectionContainer.php',
    'Automattic\\WooCommerce\\Vendor\\League\\Container\\ServiceProvider\\AbstractServiceProvider' => $baseDir . '/lib/packages/League/Container/ServiceProvider/AbstractServiceProvider.php',
    'Automattic\\WooCommerce\\Vendor\\League\\Container\\ServiceProvider\\BootableServiceProviderInterface' => $baseDir . '/lib/packages/League/Container/ServiceProvider/BootableServiceProviderInterface.php',
    'Automattic\\WooCommerce\\Vendor\\League\\Container\\ServiceProvider\\ServiceProviderAggregate' => $baseDir . '/lib/packages/League/Container/ServiceProvider/ServiceProviderAggregate.php',
    'Automattic\\WooCommerce\\Vendor\\League\\Container\\ServiceProvider\\ServiceProviderAggregateInterface' => $baseDir . '/lib/packages/League/Container/ServiceProvider/ServiceProviderAggregateInterface.php',
    'Automattic\\WooCommerce\\Vendor\\League\\Container\\ServiceProvider\\ServiceProviderInterface' => $baseDir . '/lib/packages/League/Container/ServiceProvider/ServiceProviderInterface.php',
    'Automattic\\WooCommerce\\Vendor\\Psr\\Container\\ContainerExceptionInterface' => $baseDir . '/lib/packages/Psr/Container/ContainerExceptionInterface.php',
    'Automattic\\WooCommerce\\Vendor\\Psr\\Container\\ContainerInterface' => $baseDir . '/lib/packages/Psr/Container/ContainerInterface.php',
    'Automattic\\WooCommerce\\Vendor\\Psr\\Container\\NotFoundExceptionInterface' => $baseDir . '/lib/packages/Psr/Container/NotFoundExceptionInterface.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'Composer\\Installers\\AglInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/AglInstaller.php',
    'Composer\\Installers\\AimeosInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/AimeosInstaller.php',
    'Composer\\Installers\\AnnotateCmsInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/AnnotateCmsInstaller.php',
    'Composer\\Installers\\AsgardInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/AsgardInstaller.php',
    'Composer\\Installers\\AttogramInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/AttogramInstaller.php',
    'Composer\\Installers\\BaseInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/BaseInstaller.php',
    'Composer\\Installers\\BitrixInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/BitrixInstaller.php',
    'Composer\\Installers\\BonefishInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/BonefishInstaller.php',
    'Composer\\Installers\\CakePHPInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CakePHPInstaller.php',
    'Composer\\Installers\\ChefInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ChefInstaller.php',
    'Composer\\Installers\\CiviCrmInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CiviCrmInstaller.php',
    'Composer\\Installers\\ClanCatsFrameworkInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ClanCatsFrameworkInstaller.php',
    'Composer\\Installers\\CockpitInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CockpitInstaller.php',
    'Composer\\Installers\\CodeIgniterInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CodeIgniterInstaller.php',
    'Composer\\Installers\\Concrete5Installer' => $vendorDir . '/composer/installers/src/Composer/Installers/Concrete5Installer.php',
    'Composer\\Installers\\CraftInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CraftInstaller.php',
    'Composer\\Installers\\CroogoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CroogoInstaller.php',
    'Composer\\Installers\\DecibelInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/DecibelInstaller.php',
    'Composer\\Installers\\DframeInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/DframeInstaller.php',
    'Composer\\Installers\\DokuWikiInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/DokuWikiInstaller.php',
    'Composer\\Installers\\DolibarrInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/DolibarrInstaller.php',
    'Composer\\Installers\\DrupalInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/DrupalInstaller.php',
    'Composer\\Installers\\ElggInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ElggInstaller.php',
    'Composer\\Installers\\EliasisInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/EliasisInstaller.php',
    'Composer\\Installers\\ExpressionEngineInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ExpressionEngineInstaller.php',
    'Composer\\Installers\\EzPlatformInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/EzPlatformInstaller.php',
    'Composer\\Installers\\FuelInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/FuelInstaller.php',
    'Composer\\Installers\\FuelphpInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/FuelphpInstaller.php',
    'Composer\\Installers\\GravInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/GravInstaller.php',
    'Composer\\Installers\\HuradInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/HuradInstaller.php',
    'Composer\\Installers\\ImageCMSInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ImageCMSInstaller.php',
    'Composer\\Installers\\Installer' => $vendorDir . '/composer/installers/src/Composer/Installers/Installer.php',
    'Composer\\Installers\\ItopInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ItopInstaller.php',
    'Composer\\Installers\\JoomlaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/JoomlaInstaller.php',
    'Composer\\Installers\\KanboardInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/KanboardInstaller.php',
    'Composer\\Installers\\KirbyInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/KirbyInstaller.php',
    'Composer\\Installers\\KnownInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/KnownInstaller.php',
    'Composer\\Installers\\KodiCMSInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/KodiCMSInstaller.php',
    'Composer\\Installers\\KohanaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/KohanaInstaller.php',
    'Composer\\Installers\\LanManagementSystemInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/LanManagementSystemInstaller.php',
    'Composer\\Installers\\LaravelInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/LaravelInstaller.php',
    'Composer\\Installers\\LavaLiteInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/LavaLiteInstaller.php',
    'Composer\\Installers\\LithiumInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/LithiumInstaller.php',
    'Composer\\Installers\\MODULEWorkInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MODULEWorkInstaller.php',
    'Composer\\Installers\\MODXEvoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MODXEvoInstaller.php',
    'Composer\\Installers\\MagentoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MagentoInstaller.php',
    'Composer\\Installers\\MajimaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MajimaInstaller.php',
    'Composer\\Installers\\MakoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MakoInstaller.php',
    'Composer\\Installers\\MantisBTInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MantisBTInstaller.php',
    'Composer\\Installers\\MauticInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MauticInstaller.php',
    'Composer\\Installers\\MayaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MayaInstaller.php',
    'Composer\\Installers\\MediaWikiInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MediaWikiInstaller.php',
    'Composer\\Installers\\MiaoxingInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MiaoxingInstaller.php',
    'Composer\\Installers\\MicroweberInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MicroweberInstaller.php',
    'Composer\\Installers\\ModxInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ModxInstaller.php',
    'Composer\\Installers\\MoodleInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MoodleInstaller.php',
    'Composer\\Installers\\OctoberInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/OctoberInstaller.php',
    'Composer\\Installers\\OntoWikiInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/OntoWikiInstaller.php',
    'Composer\\Installers\\OsclassInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/OsclassInstaller.php',
    'Composer\\Installers\\OxidInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/OxidInstaller.php',
    'Composer\\Installers\\PPIInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PPIInstaller.php',
    'Composer\\Installers\\PantheonInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PantheonInstaller.php',
    'Composer\\Installers\\PhiftyInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PhiftyInstaller.php',
    'Composer\\Installers\\PhpBBInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PhpBBInstaller.php',
    'Composer\\Installers\\PimcoreInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PimcoreInstaller.php',
    'Composer\\Installers\\PiwikInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PiwikInstaller.php',
    'Composer\\Installers\\PlentymarketsInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PlentymarketsInstaller.php',
    'Composer\\Installers\\Plugin' => $vendorDir . '/composer/installers/src/Composer/Installers/Plugin.php',
    'Composer\\Installers\\PortoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PortoInstaller.php',
    'Composer\\Installers\\PrestashopInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PrestashopInstaller.php',
    'Composer\\Installers\\ProcessWireInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ProcessWireInstaller.php',
    'Composer\\Installers\\PuppetInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PuppetInstaller.php',
    'Composer\\Installers\\PxcmsInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PxcmsInstaller.php',
    'Composer\\Installers\\RadPHPInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/RadPHPInstaller.php',
    'Composer\\Installers\\ReIndexInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ReIndexInstaller.php',
    'Composer\\Installers\\Redaxo5Installer' => $vendorDir . '/composer/installers/src/Composer/Installers/Redaxo5Installer.php',
    'Composer\\Installers\\RedaxoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/RedaxoInstaller.php',
    'Composer\\Installers\\RoundcubeInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/RoundcubeInstaller.php',
    'Composer\\Installers\\SMFInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/SMFInstaller.php',
    'Composer\\Installers\\ShopwareInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ShopwareInstaller.php',
    'Composer\\Installers\\SilverStripeInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/SilverStripeInstaller.php',
    'Composer\\Installers\\SiteDirectInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/SiteDirectInstaller.php',
    'Composer\\Installers\\StarbugInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/StarbugInstaller.php',
    'Composer\\Installers\\SyDESInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/SyDESInstaller.php',
    'Composer\\Installers\\SyliusInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/SyliusInstaller.php',
    'Composer\\Installers\\Symfony1Installer' => $vendorDir . '/composer/installers/src/Composer/Installers/Symfony1Installer.php',
    'Composer\\Installers\\TYPO3CmsInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TYPO3CmsInstaller.php',
    'Composer\\Installers\\TYPO3FlowInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TYPO3FlowInstaller.php',
    'Composer\\Installers\\TaoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TaoInstaller.php',
    'Composer\\Installers\\TastyIgniterInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TastyIgniterInstaller.php',
    'Composer\\Installers\\TheliaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TheliaInstaller.php',
    'Composer\\Installers\\TuskInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TuskInstaller.php',
    'Composer\\Installers\\UserFrostingInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/UserFrostingInstaller.php',
    'Composer\\Installers\\VanillaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/VanillaInstaller.php',
    'Composer\\Installers\\VgmcpInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/VgmcpInstaller.php',
    'Composer\\Installers\\WHMCSInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/WHMCSInstaller.php',
    'Composer\\Installers\\WinterInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/WinterInstaller.php',
    'Composer\\Installers\\WolfCMSInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/WolfCMSInstaller.php',
    'Composer\\Installers\\WordPressInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/WordPressInstaller.php',
    'Composer\\Installers\\YawikInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/YawikInstaller.php',
    'Composer\\Installers\\ZendInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ZendInstaller.php',
    'Composer\\Installers\\ZikulaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ZikulaInstaller.php',
    'Jetpack_IXR_Client' => $vendorDir . '/automattic/jetpack-connection/legacy/class-jetpack-ixr-client.php',
    'Jetpack_IXR_ClientMulticall' => $vendorDir . '/automattic/jetpack-connection/legacy/class-jetpack-ixr-clientmulticall.php',
    'Jetpack_Options' => $vendorDir . '/automattic/jetpack-connection/legacy/class-jetpack-options.php',
    'Jetpack_Signature' => $vendorDir . '/automattic/jetpack-connection/legacy/class-jetpack-signature.php',
    'Jetpack_Tracks_Client' => $vendorDir . '/automattic/jetpack-connection/legacy/class-jetpack-tracks-client.php',
    'Jetpack_Tracks_Event' => $vendorDir . '/automattic/jetpack-connection/legacy/class-jetpack-tracks-event.php',
    'Jetpack_XMLRPC_Server' => $vendorDir . '/automattic/jetpack-connection/legacy/class-jetpack-xmlrpc-server.php',
    'MaxMind\\Db\\Reader' => $vendorDir . '/maxmind-db/reader/src/MaxMind/Db/Reader.php',
    'MaxMind\\Db\\Reader\\Decoder' => $vendorDir . '/maxmind-db/reader/src/MaxMind/Db/Reader/Decoder.php',
    'MaxMind\\Db\\Reader\\InvalidDatabaseException' => $vendorDir . '/maxmind-db/reader/src/MaxMind/Db/Reader/InvalidDatabaseException.php',
    'MaxMind\\Db\\Reader\\Metadata' => $vendorDir . '/maxmind-db/reader/src/MaxMind/Db/Reader/Metadata.php',
    'MaxMind\\Db\\Reader\\Util' => $vendorDir . '/maxmind-db/reader/src/MaxMind/Db/Reader/Util.php',
    'Opis\\JsonSchema\\CompliantValidator' => $vendorDir . '/opis/json-schema/src/CompliantValidator.php',
    'Opis\\JsonSchema\\ContentEncoding' => $vendorDir . '/opis/json-schema/src/ContentEncoding.php',
    'Opis\\JsonSchema\\ContentMediaType' => $vendorDir . '/opis/json-schema/src/ContentMediaType.php',
    'Opis\\JsonSchema\\Errors\\CustomError' => $vendorDir . '/opis/json-schema/src/Errors/CustomError.php',
    'Opis\\JsonSchema\\Errors\\ErrorContainer' => $vendorDir . '/opis/json-schema/src/Errors/ErrorContainer.php',
    'Opis\\JsonSchema\\Errors\\ErrorFormatter' => $vendorDir . '/opis/json-schema/src/Errors/ErrorFormatter.php',
    'Opis\\JsonSchema\\Errors\\ValidationError' => $vendorDir . '/opis/json-schema/src/Errors/ValidationError.php',
    'Opis\\JsonSchema\\Exceptions\\DuplicateSchemaIdException' => $vendorDir . '/opis/json-schema/src/Exceptions/DuplicateSchemaIdException.php',
    'Opis\\JsonSchema\\Exceptions\\InvalidKeywordException' => $vendorDir . '/opis/json-schema/src/Exceptions/InvalidKeywordException.php',
    'Opis\\JsonSchema\\Exceptions\\InvalidPragmaException' => $vendorDir . '/opis/json-schema/src/Exceptions/InvalidPragmaException.php',
    'Opis\\JsonSchema\\Exceptions\\ParseException' => $vendorDir . '/opis/json-schema/src/Exceptions/ParseException.php',
    'Opis\\JsonSchema\\Exceptions\\SchemaException' => $vendorDir . '/opis/json-schema/src/Exceptions/SchemaException.php',
    'Opis\\JsonSchema\\Exceptions\\UnresolvedContentEncodingException' => $vendorDir . '/opis/json-schema/src/Exceptions/UnresolvedContentEncodingException.php',
    'Opis\\JsonSchema\\Exceptions\\UnresolvedContentMediaTypeException' => $vendorDir . '/opis/json-schema/src/Exceptions/UnresolvedContentMediaTypeException.php',
    'Opis\\JsonSchema\\Exceptions\\UnresolvedException' => $vendorDir . '/opis/json-schema/src/Exceptions/UnresolvedException.php',
    'Opis\\JsonSchema\\Exceptions\\UnresolvedFilterException' => $vendorDir . '/opis/json-schema/src/Exceptions/UnresolvedFilterException.php',
    'Opis\\JsonSchema\\Exceptions\\UnresolvedReferenceException' => $vendorDir . '/opis/json-schema/src/Exceptions/UnresolvedReferenceException.php',
    'Opis\\JsonSchema\\Filter' => $vendorDir . '/opis/json-schema/src/Filter.php',
    'Opis\\JsonSchema\\Filters\\CommonFilters' => $vendorDir . '/opis/json-schema/src/Filters/CommonFilters.php',
    'Opis\\JsonSchema\\Filters\\DataExistsFilter' => $vendorDir . '/opis/json-schema/src/Filters/DataExistsFilter.php',
    'Opis\\JsonSchema\\Filters\\DateTimeFilters' => $vendorDir . '/opis/json-schema/src/Filters/DateTimeFilters.php',
    'Opis\\JsonSchema\\Filters\\FilterExistsFilter' => $vendorDir . '/opis/json-schema/src/Filters/FilterExistsFilter.php',
    'Opis\\JsonSchema\\Filters\\FormatExistsFilter' => $vendorDir . '/opis/json-schema/src/Filters/FormatExistsFilter.php',
    'Opis\\JsonSchema\\Filters\\GlobalVarExistsFilter' => $vendorDir . '/opis/json-schema/src/Filters/GlobalVarExistsFilter.php',
    'Opis\\JsonSchema\\Filters\\SchemaExistsFilter' => $vendorDir . '/opis/json-schema/src/Filters/SchemaExistsFilter.php',
    'Opis\\JsonSchema\\Filters\\SlotExistsFilter' => $vendorDir . '/opis/json-schema/src/Filters/SlotExistsFilter.php',
    'Opis\\JsonSchema\\Format' => $vendorDir . '/opis/json-schema/src/Format.php',
    'Opis\\JsonSchema\\Formats\\DateTimeFormats' => $vendorDir . '/opis/json-schema/src/Formats/DateTimeFormats.php',
    'Opis\\JsonSchema\\Formats\\IriFormats' => $vendorDir . '/opis/json-schema/src/Formats/IriFormats.php',
    'Opis\\JsonSchema\\Formats\\MiscFormats' => $vendorDir . '/opis/json-schema/src/Formats/MiscFormats.php',
    'Opis\\JsonSchema\\Formats\\UriFormats' => $vendorDir . '/opis/json-schema/src/Formats/UriFormats.php',
    'Opis\\JsonSchema\\Helper' => $vendorDir . '/opis/json-schema/src/Helper.php',
    'Opis\\JsonSchema\\Info\\DataInfo' => $vendorDir . '/opis/json-schema/src/Info/DataInfo.php',
    'Opis\\JsonSchema\\Info\\SchemaInfo' => $vendorDir . '/opis/json-schema/src/Info/SchemaInfo.php',
    'Opis\\JsonSchema\\JsonPointer' => $vendorDir . '/opis/json-schema/src/JsonPointer.php',
    'Opis\\JsonSchema\\Keyword' => $vendorDir . '/opis/json-schema/src/Keyword.php',
    'Opis\\JsonSchema\\KeywordValidator' => $vendorDir . '/opis/json-schema/src/KeywordValidator.php',
    'Opis\\JsonSchema\\KeywordValidators\\AbstractKeywordValidator' => $vendorDir . '/opis/json-schema/src/KeywordValidators/AbstractKeywordValidator.php',
    'Opis\\JsonSchema\\KeywordValidators\\CallbackKeywordValidator' => $vendorDir . '/opis/json-schema/src/KeywordValidators/CallbackKeywordValidator.php',
    'Opis\\JsonSchema\\KeywordValidators\\PragmaKeywordValidator' => $vendorDir . '/opis/json-schema/src/KeywordValidators/PragmaKeywordValidator.php',
    'Opis\\JsonSchema\\Keywords\\AbstractRefKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/AbstractRefKeyword.php',
    'Opis\\JsonSchema\\Keywords\\AdditionalItemsKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/AdditionalItemsKeyword.php',
    'Opis\\JsonSchema\\Keywords\\AdditionalPropertiesKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/AdditionalPropertiesKeyword.php',
    'Opis\\JsonSchema\\Keywords\\AllOfKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/AllOfKeyword.php',
    'Opis\\JsonSchema\\Keywords\\AnyOfKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/AnyOfKeyword.php',
    'Opis\\JsonSchema\\Keywords\\ConstDataKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/ConstDataKeyword.php',
    'Opis\\JsonSchema\\Keywords\\ConstKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/ConstKeyword.php',
    'Opis\\JsonSchema\\Keywords\\ContainsKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/ContainsKeyword.php',
    'Opis\\JsonSchema\\Keywords\\ContentEncodingKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/ContentEncodingKeyword.php',
    'Opis\\JsonSchema\\Keywords\\ContentMediaTypeKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/ContentMediaTypeKeyword.php',
    'Opis\\JsonSchema\\Keywords\\ContentSchemaKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/ContentSchemaKeyword.php',
    'Opis\\JsonSchema\\Keywords\\DefaultKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/DefaultKeyword.php',
    'Opis\\JsonSchema\\Keywords\\DependenciesKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/DependenciesKeyword.php',
    'Opis\\JsonSchema\\Keywords\\DependentRequiredKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/DependentRequiredKeyword.php',
    'Opis\\JsonSchema\\Keywords\\DependentSchemasKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/DependentSchemasKeyword.php',
    'Opis\\JsonSchema\\Keywords\\EnumDataKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/EnumDataKeyword.php',
    'Opis\\JsonSchema\\Keywords\\EnumKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/EnumKeyword.php',
    'Opis\\JsonSchema\\Keywords\\ErrorTrait' => $vendorDir . '/opis/json-schema/src/Keywords/ErrorTrait.php',
    'Opis\\JsonSchema\\Keywords\\ExclusiveMaximumDataKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/ExclusiveMaximumDataKeyword.php',
    'Opis\\JsonSchema\\Keywords\\ExclusiveMaximumKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/ExclusiveMaximumKeyword.php',
    'Opis\\JsonSchema\\Keywords\\ExclusiveMinimumDataKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/ExclusiveMinimumDataKeyword.php',
    'Opis\\JsonSchema\\Keywords\\ExclusiveMinimumKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/ExclusiveMinimumKeyword.php',
    'Opis\\JsonSchema\\Keywords\\FiltersKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/FiltersKeyword.php',
    'Opis\\JsonSchema\\Keywords\\FormatDataKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/FormatDataKeyword.php',
    'Opis\\JsonSchema\\Keywords\\FormatKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/FormatKeyword.php',
    'Opis\\JsonSchema\\Keywords\\IfThenElseKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/IfThenElseKeyword.php',
    'Opis\\JsonSchema\\Keywords\\ItemsKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/ItemsKeyword.php',
    'Opis\\JsonSchema\\Keywords\\IterableDataValidationTrait' => $vendorDir . '/opis/json-schema/src/Keywords/IterableDataValidationTrait.php',
    'Opis\\JsonSchema\\Keywords\\MaxItemsDataKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/MaxItemsDataKeyword.php',
    'Opis\\JsonSchema\\Keywords\\MaxItemsKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/MaxItemsKeyword.php',
    'Opis\\JsonSchema\\Keywords\\MaxLengthDataKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/MaxLengthDataKeyword.php',
    'Opis\\JsonSchema\\Keywords\\MaxLengthKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/MaxLengthKeyword.php',
    'Opis\\JsonSchema\\Keywords\\MaxPropertiesDataKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/MaxPropertiesDataKeyword.php',
    'Opis\\JsonSchema\\Keywords\\MaxPropertiesKeywords' => $vendorDir . '/opis/json-schema/src/Keywords/MaxPropertiesKeywords.php',
    'Opis\\JsonSchema\\Keywords\\MaximumDataKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/MaximumDataKeyword.php',
    'Opis\\JsonSchema\\Keywords\\MaximumKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/MaximumKeyword.php',
    'Opis\\JsonSchema\\Keywords\\MinItemsDataKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/MinItemsDataKeyword.php',
    'Opis\\JsonSchema\\Keywords\\MinItemsKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/MinItemsKeyword.php',
    'Opis\\JsonSchema\\Keywords\\MinLengthDataKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/MinLengthDataKeyword.php',
    'Opis\\JsonSchema\\Keywords\\MinLengthKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/MinLengthKeyword.php',
    'Opis\\JsonSchema\\Keywords\\MinPropertiesDataKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/MinPropertiesDataKeyword.php',
    'Opis\\JsonSchema\\Keywords\\MinPropertiesKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/MinPropertiesKeyword.php',
    'Opis\\JsonSchema\\Keywords\\MinimumDataKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/MinimumDataKeyword.php',
    'Opis\\JsonSchema\\Keywords\\MinimumKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/MinimumKeyword.php',
    'Opis\\JsonSchema\\Keywords\\MultipleOfDataKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/MultipleOfDataKeyword.php',
    'Opis\\JsonSchema\\Keywords\\MultipleOfKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/MultipleOfKeyword.php',
    'Opis\\JsonSchema\\Keywords\\NotKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/NotKeyword.php',
    'Opis\\JsonSchema\\Keywords\\OfTrait' => $vendorDir . '/opis/json-schema/src/Keywords/OfTrait.php',
    'Opis\\JsonSchema\\Keywords\\OneOfKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/OneOfKeyword.php',
    'Opis\\JsonSchema\\Keywords\\PatternDataKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/PatternDataKeyword.php',
    'Opis\\JsonSchema\\Keywords\\PatternKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/PatternKeyword.php',
    'Opis\\JsonSchema\\Keywords\\PatternPropertiesKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/PatternPropertiesKeyword.php',
    'Opis\\JsonSchema\\Keywords\\PointerRefKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/PointerRefKeyword.php',
    'Opis\\JsonSchema\\Keywords\\PropertiesKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/PropertiesKeyword.php',
    'Opis\\JsonSchema\\Keywords\\PropertyNamesKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/PropertyNamesKeyword.php',
    'Opis\\JsonSchema\\Keywords\\RecursiveRefKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/RecursiveRefKeyword.php',
    'Opis\\JsonSchema\\Keywords\\RequiredDataKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/RequiredDataKeyword.php',
    'Opis\\JsonSchema\\Keywords\\RequiredKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/RequiredKeyword.php',
    'Opis\\JsonSchema\\Keywords\\SlotsKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/SlotsKeyword.php',
    'Opis\\JsonSchema\\Keywords\\TemplateRefKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/TemplateRefKeyword.php',
    'Opis\\JsonSchema\\Keywords\\TypeKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/TypeKeyword.php',
    'Opis\\JsonSchema\\Keywords\\URIRefKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/URIRefKeyword.php',
    'Opis\\JsonSchema\\Keywords\\UnevaluatedItemsKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/UnevaluatedItemsKeyword.php',
    'Opis\\JsonSchema\\Keywords\\UnevaluatedPropertiesKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/UnevaluatedPropertiesKeyword.php',
    'Opis\\JsonSchema\\Keywords\\UniqueItemsDataKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/UniqueItemsDataKeyword.php',
    'Opis\\JsonSchema\\Keywords\\UniqueItemsKeyword' => $vendorDir . '/opis/json-schema/src/Keywords/UniqueItemsKeyword.php',
    'Opis\\JsonSchema\\Parsers\\DataKeywordTrait' => $vendorDir . '/opis/json-schema/src/Parsers/DataKeywordTrait.php',
    'Opis\\JsonSchema\\Parsers\\DefaultVocabulary' => $vendorDir . '/opis/json-schema/src/Parsers/DefaultVocabulary.php',
    'Opis\\JsonSchema\\Parsers\\Draft' => $vendorDir . '/opis/json-schema/src/Parsers/Draft.php',
    'Opis\\JsonSchema\\Parsers\\DraftOptionTrait' => $vendorDir . '/opis/json-schema/src/Parsers/DraftOptionTrait.php',
    'Opis\\JsonSchema\\Parsers\\Drafts\\Draft06' => $vendorDir . '/opis/json-schema/src/Parsers/Drafts/Draft06.php',
    'Opis\\JsonSchema\\Parsers\\Drafts\\Draft07' => $vendorDir . '/opis/json-schema/src/Parsers/Drafts/Draft07.php',
    'Opis\\JsonSchema\\Parsers\\Drafts\\Draft201909' => $vendorDir . '/opis/json-schema/src/Parsers/Drafts/Draft201909.php',
    'Opis\\JsonSchema\\Parsers\\Drafts\\Draft202012' => $vendorDir . '/opis/json-schema/src/Parsers/Drafts/Draft202012.php',
    'Opis\\JsonSchema\\Parsers\\KeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/KeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\KeywordParserTrait' => $vendorDir . '/opis/json-schema/src/Parsers/KeywordParserTrait.php',
    'Opis\\JsonSchema\\Parsers\\KeywordValidatorParser' => $vendorDir . '/opis/json-schema/src/Parsers/KeywordValidatorParser.php',
    'Opis\\JsonSchema\\Parsers\\KeywordValidators\\PragmaKeywordValidatorParser' => $vendorDir . '/opis/json-schema/src/Parsers/KeywordValidators/PragmaKeywordValidatorParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\AdditionalItemsKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/AdditionalItemsKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\AdditionalPropertiesKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/AdditionalPropertiesKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\AllOfKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/AllOfKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\AnyOfKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/AnyOfKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\ConstKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/ConstKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\ContainsKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/ContainsKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\ContentEncodingKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/ContentEncodingKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\ContentMediaTypeKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/ContentMediaTypeKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\ContentSchemaKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/ContentSchemaKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\DefaultKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/DefaultKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\DependenciesKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/DependenciesKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\DependentRequiredKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/DependentRequiredKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\DependentSchemasKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/DependentSchemasKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\EnumKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/EnumKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\ExclusiveMaximumKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/ExclusiveMaximumKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\ExclusiveMinimumKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/ExclusiveMinimumKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\FiltersKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/FiltersKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\FormatKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/FormatKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\IfThenElseKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/IfThenElseKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\ItemsKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/ItemsKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\MaxItemsKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/MaxItemsKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\MaxLengthKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/MaxLengthKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\MaxPropertiesKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/MaxPropertiesKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\MaximumKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/MaximumKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\MinItemsKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/MinItemsKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\MinLengthKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/MinLengthKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\MinPropertiesKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/MinPropertiesKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\MinimumKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/MinimumKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\MultipleOfKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/MultipleOfKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\NotKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/NotKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\OneOfKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/OneOfKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\PatternKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/PatternKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\PatternPropertiesKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/PatternPropertiesKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\PropertiesKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/PropertiesKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\PropertyNamesKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/PropertyNamesKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\RefKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/RefKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\RequiredKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/RequiredKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\SlotsKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/SlotsKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\TypeKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/TypeKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\UnevaluatedItemsKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/UnevaluatedItemsKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\UnevaluatedPropertiesKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/UnevaluatedPropertiesKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\Keywords\\UniqueItemsKeywordParser' => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/UniqueItemsKeywordParser.php',
    'Opis\\JsonSchema\\Parsers\\PragmaParser' => $vendorDir . '/opis/json-schema/src/Parsers/PragmaParser.php',
    'Opis\\JsonSchema\\Parsers\\Pragmas\\CastPragmaParser' => $vendorDir . '/opis/json-schema/src/Parsers/Pragmas/CastPragmaParser.php',
    'Opis\\JsonSchema\\Parsers\\Pragmas\\GlobalsPragmaParser' => $vendorDir . '/opis/json-schema/src/Parsers/Pragmas/GlobalsPragmaParser.php',
    'Opis\\JsonSchema\\Parsers\\Pragmas\\MaxErrorsPragmaParser' => $vendorDir . '/opis/json-schema/src/Parsers/Pragmas/MaxErrorsPragmaParser.php',
    'Opis\\JsonSchema\\Parsers\\Pragmas\\SlotsPragmaParser' => $vendorDir . '/opis/json-schema/src/Parsers/Pragmas/SlotsPragmaParser.php',
    'Opis\\JsonSchema\\Parsers\\ResolverTrait' => $vendorDir . '/opis/json-schema/src/Parsers/ResolverTrait.php',
    'Opis\\JsonSchema\\Parsers\\SchemaParser' => $vendorDir . '/opis/json-schema/src/Parsers/SchemaParser.php',
    'Opis\\JsonSchema\\Parsers\\VariablesTrait' => $vendorDir . '/opis/json-schema/src/Parsers/VariablesTrait.php',
    'Opis\\JsonSchema\\Parsers\\Vocabulary' => $vendorDir . '/opis/json-schema/src/Parsers/Vocabulary.php',
    'Opis\\JsonSchema\\Pragma' => $vendorDir . '/opis/json-schema/src/Pragma.php',
    'Opis\\JsonSchema\\Pragmas\\CastPragma' => $vendorDir . '/opis/json-schema/src/Pragmas/CastPragma.php',
    'Opis\\JsonSchema\\Pragmas\\GlobalsPragma' => $vendorDir . '/opis/json-schema/src/Pragmas/GlobalsPragma.php',
    'Opis\\JsonSchema\\Pragmas\\MaxErrorsPragma' => $vendorDir . '/opis/json-schema/src/Pragmas/MaxErrorsPragma.php',
    'Opis\\JsonSchema\\Pragmas\\SlotsPragma' => $vendorDir . '/opis/json-schema/src/Pragmas/SlotsPragma.php',
    'Opis\\JsonSchema\\Resolvers\\ContentEncodingResolver' => $vendorDir . '/opis/json-schema/src/Resolvers/ContentEncodingResolver.php',
    'Opis\\JsonSchema\\Resolvers\\ContentMediaTypeResolver' => $vendorDir . '/opis/json-schema/src/Resolvers/ContentMediaTypeResolver.php',
    'Opis\\JsonSchema\\Resolvers\\FilterResolver' => $vendorDir . '/opis/json-schema/src/Resolvers/FilterResolver.php',
    'Opis\\JsonSchema\\Resolvers\\FormatResolver' => $vendorDir . '/opis/json-schema/src/Resolvers/FormatResolver.php',
    'Opis\\JsonSchema\\Resolvers\\SchemaResolver' => $vendorDir . '/opis/json-schema/src/Resolvers/SchemaResolver.php',
    'Opis\\JsonSchema\\Schema' => $vendorDir . '/opis/json-schema/src/Schema.php',
    'Opis\\JsonSchema\\SchemaLoader' => $vendorDir . '/opis/json-schema/src/SchemaLoader.php',
    'Opis\\JsonSchema\\SchemaValidator' => $vendorDir . '/opis/json-schema/src/SchemaValidator.php',
    'Opis\\JsonSchema\\Schemas\\AbstractSchema' => $vendorDir . '/opis/json-schema/src/Schemas/AbstractSchema.php',
    'Opis\\JsonSchema\\Schemas\\BooleanSchema' => $vendorDir . '/opis/json-schema/src/Schemas/BooleanSchema.php',
    'Opis\\JsonSchema\\Schemas\\EmptySchema' => $vendorDir . '/opis/json-schema/src/Schemas/EmptySchema.php',
    'Opis\\JsonSchema\\Schemas\\ExceptionSchema' => $vendorDir . '/opis/json-schema/src/Schemas/ExceptionSchema.php',
    'Opis\\JsonSchema\\Schemas\\LazySchema' => $vendorDir . '/opis/json-schema/src/Schemas/LazySchema.php',
    'Opis\\JsonSchema\\Schemas\\ObjectSchema' => $vendorDir . '/opis/json-schema/src/Schemas/ObjectSchema.php',
    'Opis\\JsonSchema\\Uri' => $vendorDir . '/opis/json-schema/src/Uri.php',
    'Opis\\JsonSchema\\ValidationContext' => $vendorDir . '/opis/json-schema/src/ValidationContext.php',
    'Opis\\JsonSchema\\ValidationResult' => $vendorDir . '/opis/json-schema/src/ValidationResult.php',
    'Opis\\JsonSchema\\Validator' => $vendorDir . '/opis/json-schema/src/Validator.php',
    'Opis\\JsonSchema\\Variables' => $vendorDir . '/opis/json-schema/src/Variables.php',
    'Opis\\JsonSchema\\Variables\\RefVariablesContainer' => $vendorDir . '/opis/json-schema/src/Variables/RefVariablesContainer.php',
    'Opis\\JsonSchema\\Variables\\VariablesContainer' => $vendorDir . '/opis/json-schema/src/Variables/VariablesContainer.php',
    'Opis\\String\\Exception\\InvalidCodePointException' => $vendorDir . '/opis/string/src/Exception/InvalidCodePointException.php',
    'Opis\\String\\Exception\\InvalidStringException' => $vendorDir . '/opis/string/src/Exception/InvalidStringException.php',
    'Opis\\String\\Exception\\UnicodeException' => $vendorDir . '/opis/string/src/Exception/UnicodeException.php',
    'Opis\\String\\UnicodeString' => $vendorDir . '/opis/string/src/UnicodeString.php',
    'Opis\\Uri\\Punycode' => $vendorDir . '/opis/uri/src/Punycode.php',
    'Opis\\Uri\\PunycodeException' => $vendorDir . '/opis/uri/src/PunycodeException.php',
    'Opis\\Uri\\Uri' => $vendorDir . '/opis/uri/src/Uri.php',
    'Opis\\Uri\\UriTemplate' => $vendorDir . '/opis/uri/src/UriTemplate.php',
    'Pelago\\Emogrifier\\Caching\\SimpleStringCache' => $vendorDir . '/pelago/emogrifier/src/Caching/SimpleStringCache.php',
    'Pelago\\Emogrifier\\CssInliner' => $vendorDir . '/pelago/emogrifier/src/CssInliner.php',
    'Pelago\\Emogrifier\\Css\\CssDocument' => $vendorDir . '/pelago/emogrifier/src/Css/CssDocument.php',
    'Pelago\\Emogrifier\\Css\\StyleRule' => $vendorDir . '/pelago/emogrifier/src/Css/StyleRule.php',
    'Pelago\\Emogrifier\\HtmlProcessor\\AbstractHtmlProcessor' => $vendorDir . '/pelago/emogrifier/src/HtmlProcessor/AbstractHtmlProcessor.php',
    'Pelago\\Emogrifier\\HtmlProcessor\\CssToAttributeConverter' => $vendorDir . '/pelago/emogrifier/src/HtmlProcessor/CssToAttributeConverter.php',
    'Pelago\\Emogrifier\\HtmlProcessor\\HtmlNormalizer' => $vendorDir . '/pelago/emogrifier/src/HtmlProcessor/HtmlNormalizer.php',
    'Pelago\\Emogrifier\\HtmlProcessor\\HtmlPruner' => $vendorDir . '/pelago/emogrifier/src/HtmlProcessor/HtmlPruner.php',
    'Pelago\\Emogrifier\\Utilities\\ArrayIntersector' => $vendorDir . '/pelago/emogrifier/src/Utilities/ArrayIntersector.php',
    'Pelago\\Emogrifier\\Utilities\\CssConcatenator' => $vendorDir . '/pelago/emogrifier/src/Utilities/CssConcatenator.php',
    'PhpToken' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/PhpToken.php',
    'Sabberworm\\CSS\\CSSList\\AtRuleBlockList' => $vendorDir . '/sabberworm/php-css-parser/src/CSSList/AtRuleBlockList.php',
    'Sabberworm\\CSS\\CSSList\\CSSBlockList' => $vendorDir . '/sabberworm/php-css-parser/src/CSSList/CSSBlockList.php',
    'Sabberworm\\CSS\\CSSList\\CSSList' => $vendorDir . '/sabberworm/php-css-parser/src/CSSList/CSSList.php',
    'Sabberworm\\CSS\\CSSList\\Document' => $vendorDir . '/sabberworm/php-css-parser/src/CSSList/Document.php',
    'Sabberworm\\CSS\\CSSList\\KeyFrame' => $vendorDir . '/sabberworm/php-css-parser/src/CSSList/KeyFrame.php',
    'Sabberworm\\CSS\\Comment\\Comment' => $vendorDir . '/sabberworm/php-css-parser/src/Comment/Comment.php',
    'Sabberworm\\CSS\\Comment\\Commentable' => $vendorDir . '/sabberworm/php-css-parser/src/Comment/Commentable.php',
    'Sabberworm\\CSS\\OutputFormat' => $vendorDir . '/sabberworm/php-css-parser/src/OutputFormat.php',
    'Sabberworm\\CSS\\OutputFormatter' => $vendorDir . '/sabberworm/php-css-parser/src/OutputFormatter.php',
    'Sabberworm\\CSS\\Parser' => $vendorDir . '/sabberworm/php-css-parser/src/Parser.php',
    'Sabberworm\\CSS\\Parsing\\Anchor' => $vendorDir . '/sabberworm/php-css-parser/src/Parsing/Anchor.php',
    'Sabberworm\\CSS\\Parsing\\OutputException' => $vendorDir . '/sabberworm/php-css-parser/src/Parsing/OutputException.php',
    'Sabberworm\\CSS\\Parsing\\ParserState' => $vendorDir . '/sabberworm/php-css-parser/src/Parsing/ParserState.php',
    'Sabberworm\\CSS\\Parsing\\SourceException' => $vendorDir . '/sabberworm/php-css-parser/src/Parsing/SourceException.php',
    'Sabberworm\\CSS\\Parsing\\UnexpectedEOFException' => $vendorDir . '/sabberworm/php-css-parser/src/Parsing/UnexpectedEOFException.php',
    'Sabberworm\\CSS\\Parsing\\UnexpectedTokenException' => $vendorDir . '/sabberworm/php-css-parser/src/Parsing/UnexpectedTokenException.php',
    'Sabberworm\\CSS\\Property\\AtRule' => $vendorDir . '/sabberworm/php-css-parser/src/Property/AtRule.php',
    'Sabberworm\\CSS\\Property\\CSSNamespace' => $vendorDir . '/sabberworm/php-css-parser/src/Property/CSSNamespace.php',
    'Sabberworm\\CSS\\Property\\Charset' => $vendorDir . '/sabberworm/php-css-parser/src/Property/Charset.php',
    'Sabberworm\\CSS\\Property\\Import' => $vendorDir . '/sabberworm/php-css-parser/src/Property/Import.php',
    'Sabberworm\\CSS\\Property\\KeyframeSelector' => $vendorDir . '/sabberworm/php-css-parser/src/Property/KeyframeSelector.php',
    'Sabberworm\\CSS\\Property\\Selector' => $vendorDir . '/sabberworm/php-css-parser/src/Property/Selector.php',
    'Sabberworm\\CSS\\Renderable' => $vendorDir . '/sabberworm/php-css-parser/src/Renderable.php',
    'Sabberworm\\CSS\\RuleSet\\AtRuleSet' => $vendorDir . '/sabberworm/php-css-parser/src/RuleSet/AtRuleSet.php',
    'Sabberworm\\CSS\\RuleSet\\DeclarationBlock' => $vendorDir . '/sabberworm/php-css-parser/src/RuleSet/DeclarationBlock.php',
    'Sabberworm\\CSS\\RuleSet\\RuleSet' => $vendorDir . '/sabberworm/php-css-parser/src/RuleSet/RuleSet.php',
    'Sabberworm\\CSS\\Rule\\Rule' => $vendorDir . '/sabberworm/php-css-parser/src/Rule/Rule.php',
    'Sabberworm\\CSS\\Settings' => $vendorDir . '/sabberworm/php-css-parser/src/Settings.php',
    'Sabberworm\\CSS\\Value\\CSSFunction' => $vendorDir . '/sabberworm/php-css-parser/src/Value/CSSFunction.php',
    'Sabberworm\\CSS\\Value\\CSSString' => $vendorDir . '/sabberworm/php-css-parser/src/Value/CSSString.php',
    'Sabberworm\\CSS\\Value\\CalcFunction' => $vendorDir . '/sabberworm/php-css-parser/src/Value/CalcFunction.php',
    'Sabberworm\\CSS\\Value\\CalcRuleValueList' => $vendorDir . '/sabberworm/php-css-parser/src/Value/CalcRuleValueList.php',
    'Sabberworm\\CSS\\Value\\Color' => $vendorDir . '/sabberworm/php-css-parser/src/Value/Color.php',
    'Sabberworm\\CSS\\Value\\LineName' => $vendorDir . '/sabberworm/php-css-parser/src/Value/LineName.php',
    'Sabberworm\\CSS\\Value\\PrimitiveValue' => $vendorDir . '/sabberworm/php-css-parser/src/Value/PrimitiveValue.php',
    'Sabberworm\\CSS\\Value\\RuleValueList' => $vendorDir . '/sabberworm/php-css-parser/src/Value/RuleValueList.php',
    'Sabberworm\\CSS\\Value\\Size' => $vendorDir . '/sabberworm/php-css-parser/src/Value/Size.php',
    'Sabberworm\\CSS\\Value\\URL' => $vendorDir . '/sabberworm/php-css-parser/src/Value/URL.php',
    'Sabberworm\\CSS\\Value\\Value' => $vendorDir . '/sabberworm/php-css-parser/src/Value/Value.php',
    'Sabberworm\\CSS\\Value\\ValueList' => $vendorDir . '/sabberworm/php-css-parser/src/Value/ValueList.php',
    'Soundasleep\\Html2Text' => $vendorDir . '/soundasleep/html2text/src/Html2Text.php',
    'Soundasleep\\Html2TextException' => $vendorDir . '/soundasleep/html2text/src/Html2TextException.php',
    'Stringable' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/Stringable.php',
    'Symfony\\Component\\CssSelector\\CssSelectorConverter' => $vendorDir . '/symfony/css-selector/CssSelectorConverter.php',
    'Symfony\\Component\\CssSelector\\Exception\\ExceptionInterface' => $vendorDir . '/symfony/css-selector/Exception/ExceptionInterface.php',
    'Symfony\\Component\\CssSelector\\Exception\\ExpressionErrorException' => $vendorDir . '/symfony/css-selector/Exception/ExpressionErrorException.php',
    'Symfony\\Component\\CssSelector\\Exception\\InternalErrorException' => $vendorDir . '/symfony/css-selector/Exception/InternalErrorException.php',
    'Symfony\\Component\\CssSelector\\Exception\\ParseException' => $vendorDir . '/symfony/css-selector/Exception/ParseException.php',
    'Symfony\\Component\\CssSelector\\Exception\\SyntaxErrorException' => $vendorDir . '/symfony/css-selector/Exception/SyntaxErrorException.php',
    'Symfony\\Component\\CssSelector\\Node\\AbstractNode' => $vendorDir . '/symfony/css-selector/Node/AbstractNode.php',
    'Symfony\\Component\\CssSelector\\Node\\AttributeNode' => $vendorDir . '/symfony/css-selector/Node/AttributeNode.php',
    'Symfony\\Component\\CssSelector\\Node\\ClassNode' => $vendorDir . '/symfony/css-selector/Node/ClassNode.php',
    'Symfony\\Component\\CssSelector\\Node\\CombinedSelectorNode' => $vendorDir . '/symfony/css-selector/Node/CombinedSelectorNode.php',
    'Symfony\\Component\\CssSelector\\Node\\ElementNode' => $vendorDir . '/symfony/css-selector/Node/ElementNode.php',
    'Symfony\\Component\\CssSelector\\Node\\FunctionNode' => $vendorDir . '/symfony/css-selector/Node/FunctionNode.php',
    'Symfony\\Component\\CssSelector\\Node\\HashNode' => $vendorDir . '/symfony/css-selector/Node/HashNode.php',
    'Symfony\\Component\\CssSelector\\Node\\NegationNode' => $vendorDir . '/symfony/css-selector/Node/NegationNode.php',
    'Symfony\\Component\\CssSelector\\Node\\NodeInterface' => $vendorDir . '/symfony/css-selector/Node/NodeInterface.php',
    'Symfony\\Component\\CssSelector\\Node\\PseudoNode' => $vendorDir . '/symfony/css-selector/Node/PseudoNode.php',
    'Symfony\\Component\\CssSelector\\Node\\SelectorNode' => $vendorDir . '/symfony/css-selector/Node/SelectorNode.php',
    'Symfony\\Component\\CssSelector\\Node\\Specificity' => $vendorDir . '/symfony/css-selector/Node/Specificity.php',
    'Symfony\\Component\\CssSelector\\Parser\\Handler\\CommentHandler' => $vendorDir . '/symfony/css-selector/Parser/Handler/CommentHandler.php',
    'Symfony\\Component\\CssSelector\\Parser\\Handler\\HandlerInterface' => $vendorDir . '/symfony/css-selector/Parser/Handler/HandlerInterface.php',
    'Symfony\\Component\\CssSelector\\Parser\\Handler\\HashHandler' => $vendorDir . '/symfony/css-selector/Parser/Handler/HashHandler.php',
    'Symfony\\Component\\CssSelector\\Parser\\Handler\\IdentifierHandler' => $vendorDir . '/symfony/css-selector/Parser/Handler/IdentifierHandler.php',
    'Symfony\\Component\\CssSelector\\Parser\\Handler\\NumberHandler' => $vendorDir . '/symfony/css-selector/Parser/Handler/NumberHandler.php',
    'Symfony\\Component\\CssSelector\\Parser\\Handler\\StringHandler' => $vendorDir . '/symfony/css-selector/Parser/Handler/StringHandler.php',
    'Symfony\\Component\\CssSelector\\Parser\\Handler\\WhitespaceHandler' => $vendorDir . '/symfony/css-selector/Parser/Handler/WhitespaceHandler.php',
    'Symfony\\Component\\CssSelector\\Parser\\Parser' => $vendorDir . '/symfony/css-selector/Parser/Parser.php',
    'Symfony\\Component\\CssSelector\\Parser\\ParserInterface' => $vendorDir . '/symfony/css-selector/Parser/ParserInterface.php',
    'Symfony\\Component\\CssSelector\\Parser\\Reader' => $vendorDir . '/symfony/css-selector/Parser/Reader.php',
    'Symfony\\Component\\CssSelector\\Parser\\Shortcut\\ClassParser' => $vendorDir . '/symfony/css-selector/Parser/Shortcut/ClassParser.php',
    'Symfony\\Component\\CssSelector\\Parser\\Shortcut\\ElementParser' => $vendorDir . '/symfony/css-selector/Parser/Shortcut/ElementParser.php',
    'Symfony\\Component\\CssSelector\\Parser\\Shortcut\\EmptyStringParser' => $vendorDir . '/symfony/css-selector/Parser/Shortcut/EmptyStringParser.php',
    'Symfony\\Component\\CssSelector\\Parser\\Shortcut\\HashParser' => $vendorDir . '/symfony/css-selector/Parser/Shortcut/HashParser.php',
    'Symfony\\Component\\CssSelector\\Parser\\Token' => $vendorDir . '/symfony/css-selector/Parser/Token.php',
    'Symfony\\Component\\CssSelector\\Parser\\TokenStream' => $vendorDir . '/symfony/css-selector/Parser/TokenStream.php',
    'Symfony\\Component\\CssSelector\\Parser\\Tokenizer\\Tokenizer' => $vendorDir . '/symfony/css-selector/Parser/Tokenizer/Tokenizer.php',
    'Symfony\\Component\\CssSelector\\Parser\\Tokenizer\\TokenizerEscaping' => $vendorDir . '/symfony/css-selector/Parser/Tokenizer/TokenizerEscaping.php',
    'Symfony\\Component\\CssSelector\\Parser\\Tokenizer\\TokenizerPatterns' => $vendorDir . '/symfony/css-selector/Parser/Tokenizer/TokenizerPatterns.php',
    'Symfony\\Component\\CssSelector\\XPath\\Extension\\AbstractExtension' => $vendorDir . '/symfony/css-selector/XPath/Extension/AbstractExtension.php',
    'Symfony\\Component\\CssSelector\\XPath\\Extension\\AttributeMatchingExtension' => $vendorDir . '/symfony/css-selector/XPath/Extension/AttributeMatchingExtension.php',
    'Symfony\\Component\\CssSelector\\XPath\\Extension\\CombinationExtension' => $vendorDir . '/symfony/css-selector/XPath/Extension/CombinationExtension.php',
    'Symfony\\Component\\CssSelector\\XPath\\Extension\\ExtensionInterface' => $vendorDir . '/symfony/css-selector/XPath/Extension/ExtensionInterface.php',
    'Symfony\\Component\\CssSelector\\XPath\\Extension\\FunctionExtension' => $vendorDir . '/symfony/css-selector/XPath/Extension/FunctionExtension.php',
    'Symfony\\Component\\CssSelector\\XPath\\Extension\\HtmlExtension' => $vendorDir . '/symfony/css-selector/XPath/Extension/HtmlExtension.php',
    'Symfony\\Component\\CssSelector\\XPath\\Extension\\NodeExtension' => $vendorDir . '/symfony/css-selector/XPath/Extension/NodeExtension.php',
    'Symfony\\Component\\CssSelector\\XPath\\Extension\\PseudoClassExtension' => $vendorDir . '/symfony/css-selector/XPath/Extension/PseudoClassExtension.php',
    'Symfony\\Component\\CssSelector\\XPath\\Translator' => $vendorDir . '/symfony/css-selector/XPath/Translator.php',
    'Symfony\\Component\\CssSelector\\XPath\\TranslatorInterface' => $vendorDir . '/symfony/css-selector/XPath/TranslatorInterface.php',
    'Symfony\\Component\\CssSelector\\XPath\\XPathExpr' => $vendorDir . '/symfony/css-selector/XPath/XPathExpr.php',
    'Symfony\\Polyfill\\Php80\\Php80' => $vendorDir . '/symfony/polyfill-php80/Php80.php',
    'Symfony\\Polyfill\\Php80\\PhpToken' => $vendorDir . '/symfony/polyfill-php80/PhpToken.php',
    'UnhandledMatchError' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/UnhandledMatchError.php',
    'ValueError' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/ValueError.php',
    'WC_REST_CRUD_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-crud-controller.php',
    'WC_REST_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-controller.php',
    'WC_REST_Coupons_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-coupons-controller.php',
    'WC_REST_Coupons_V1_Controller' => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-coupons-v1-controller.php',
    'WC_REST_Coupons_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-coupons-v2-controller.php',
    'WC_REST_Customer_Downloads_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-customer-downloads-controller.php',
    'WC_REST_Customer_Downloads_V1_Controller' => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-customer-downloads-v1-controller.php',
    'WC_REST_Customer_Downloads_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-customer-downloads-v2-controller.php',
    'WC_REST_Customers_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-customers-controller.php',
    'WC_REST_Customers_V1_Controller' => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-customers-v1-controller.php',
    'WC_REST_Customers_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-customers-v2-controller.php',
    'WC_REST_Data_Continents_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-data-continents-controller.php',
    'WC_REST_Data_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-data-controller.php',
    'WC_REST_Data_Countries_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-data-countries-controller.php',
    'WC_REST_Data_Currencies_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-data-currencies-controller.php',
    'WC_REST_Layout_Templates_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-layout-templates-controller.php',
    'WC_REST_Network_Orders_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-network-orders-controller.php',
    'WC_REST_Network_Orders_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-network-orders-v2-controller.php',
    'WC_REST_Order_Notes_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-order-notes-controller.php',
    'WC_REST_Order_Notes_V1_Controller' => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-order-notes-v1-controller.php',
    'WC_REST_Order_Notes_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-order-notes-v2-controller.php',
    'WC_REST_Order_Refunds_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-order-refunds-controller.php',
    'WC_REST_Order_Refunds_V1_Controller' => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-order-refunds-v1-controller.php',
    'WC_REST_Order_Refunds_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-order-refunds-v2-controller.php',
    'WC_REST_Orders_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-orders-controller.php',
    'WC_REST_Orders_V1_Controller' => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-orders-v1-controller.php',
    'WC_REST_Orders_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-orders-v2-controller.php',
    'WC_REST_Payment_Gateways_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-payment-gateways-controller.php',
    'WC_REST_Payment_Gateways_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-payment-gateways-v2-controller.php',
    'WC_REST_Posts_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-posts-controller.php',
    'WC_REST_Product_Attribute_Terms_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-product-attribute-terms-controller.php',
    'WC_REST_Product_Attribute_Terms_V1_Controller' => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-product-attribute-terms-v1-controller.php',
    'WC_REST_Product_Attribute_Terms_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-product-attribute-terms-v2-controller.php',
    'WC_REST_Product_Attributes_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-product-attributes-controller.php',
    'WC_REST_Product_Attributes_V1_Controller' => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-product-attributes-v1-controller.php',
    'WC_REST_Product_Attributes_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-product-attributes-v2-controller.php',
    'WC_REST_Product_Brands_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-product-brands-controller.php',
    'WC_REST_Product_Brands_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-product-brands-v2-controller.php',
    'WC_REST_Product_Categories_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-product-categories-controller.php',
    'WC_REST_Product_Categories_V1_Controller' => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-product-categories-v1-controller.php',
    'WC_REST_Product_Categories_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-product-categories-v2-controller.php',
    'WC_REST_Product_Custom_Fields_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-product-custom-fields-controller.php',
    'WC_REST_Product_Reviews_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-product-reviews-controller.php',
    'WC_REST_Product_Reviews_V1_Controller' => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-product-reviews-v1-controller.php',
    'WC_REST_Product_Reviews_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-product-reviews-v2-controller.php',
    'WC_REST_Product_Shipping_Classes_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-product-shipping-classes-controller.php',
    'WC_REST_Product_Shipping_Classes_V1_Controller' => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-product-shipping-classes-v1-controller.php',
    'WC_REST_Product_Shipping_Classes_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-product-shipping-classes-v2-controller.php',
    'WC_REST_Product_Tags_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-product-tags-controller.php',
    'WC_REST_Product_Tags_V1_Controller' => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-product-tags-v1-controller.php',
    'WC_REST_Product_Tags_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-product-tags-v2-controller.php',
    'WC_REST_Product_Variations_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-product-variations-controller.php',
    'WC_REST_Product_Variations_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-product-variations-v2-controller.php',
    'WC_REST_Products_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-products-controller.php',
    'WC_REST_Products_V1_Controller' => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-products-v1-controller.php',
    'WC_REST_Products_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-products-v2-controller.php',
    'WC_REST_Refunds_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-refunds-controller.php',
    'WC_REST_Report_Coupons_Totals_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-report-coupons-totals-controller.php',
    'WC_REST_Report_Customers_Totals_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-report-customers-totals-controller.php',
    'WC_REST_Report_Orders_Totals_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-report-orders-totals-controller.php',
    'WC_REST_Report_Products_Totals_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-report-products-totals-controller.php',
    'WC_REST_Report_Reviews_Totals_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-report-reviews-totals-controller.php',
    'WC_REST_Report_Sales_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-report-sales-controller.php',
    'WC_REST_Report_Sales_V1_Controller' => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-report-sales-v1-controller.php',
    'WC_REST_Report_Sales_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-report-sales-v2-controller.php',
    'WC_REST_Report_Top_Sellers_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-report-top-sellers-controller.php',
    'WC_REST_Report_Top_Sellers_V1_Controller' => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-report-top-sellers-v1-controller.php',
    'WC_REST_Report_Top_Sellers_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-report-top-sellers-v2-controller.php',
    'WC_REST_Reports_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-reports-controller.php',
    'WC_REST_Reports_V1_Controller' => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-reports-v1-controller.php',
    'WC_REST_Reports_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-reports-v2-controller.php',
    'WC_REST_Setting_Options_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-setting-options-controller.php',
    'WC_REST_Setting_Options_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-setting-options-v2-controller.php',
    'WC_REST_Settings_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-settings-controller.php',
    'WC_REST_Settings_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-settings-v2-controller.php',
    'WC_REST_Shipping_Methods_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-shipping-methods-controller.php',
    'WC_REST_Shipping_Methods_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-shipping-methods-v2-controller.php',
    'WC_REST_Shipping_Zone_Locations_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-shipping-zone-locations-controller.php',
    'WC_REST_Shipping_Zone_Locations_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-shipping-zone-locations-v2-controller.php',
    'WC_REST_Shipping_Zone_Methods_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-shipping-zone-methods-controller.php',
    'WC_REST_Shipping_Zone_Methods_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-shipping-zone-methods-v2-controller.php',
    'WC_REST_Shipping_Zones_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-shipping-zones-controller.php',
    'WC_REST_Shipping_Zones_Controller_Base' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-shipping-zones-controller-base.php',
    'WC_REST_Shipping_Zones_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-shipping-zones-v2-controller.php',
    'WC_REST_System_Status_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-system-status-controller.php',
    'WC_REST_System_Status_Tools_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-system-status-tools-controller.php',
    'WC_REST_System_Status_Tools_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-system-status-tools-v2-controller.php',
    'WC_REST_System_Status_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-system-status-v2-controller.php',
    'WC_REST_Tax_Classes_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-tax-classes-controller.php',
    'WC_REST_Tax_Classes_V1_Controller' => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-tax-classes-v1-controller.php',
    'WC_REST_Tax_Classes_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-tax-classes-v2-controller.php',
    'WC_REST_Taxes_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-taxes-controller.php',
    'WC_REST_Taxes_V1_Controller' => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-taxes-v1-controller.php',
    'WC_REST_Taxes_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-taxes-v2-controller.php',
    'WC_REST_Telemetry_Controller' => $baseDir . '/includes/rest-api/Controllers/Telemetry/class-wc-rest-telemetry-controller.php',
    'WC_REST_Terms_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-terms-controller.php',
    'WC_REST_Webhook_Deliveries_V1_Controller' => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-webhook-deliveries-v1-controller.php',
    'WC_REST_Webhook_Deliveries_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-webhook-deliveries-v2-controller.php',
    'WC_REST_Webhooks_Controller' => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-webhooks-controller.php',
    'WC_REST_Webhooks_V1_Controller' => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-webhooks-v1-controller.php',
    'WC_REST_Webhooks_V2_Controller' => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-webhooks-v2-controller.php',
);
