<?php
/**
 * Autoloader file writer.
 *
 * @package automattic/jetpack-autoloader
 */

namespace Automattic\Jetpack\Autoloader;

use Composer\IO\IOInterface;

/**
 * Class AutoloadFileWriter.
 */
class AutoloadFileWriter {

	/**
	 * The file comment to use.
	 */
	const COMMENT = <<<AUTOLOADER_COMMENT
/**
 * This file was automatically generated by automattic/jetpack-autoloader.
 *
 * @package automattic/jetpack-autoloader
 */

AUTOLOADER_COMMENT;

	/**
	 * Copies autoloader files and replaces any placeholders in them.
	 *
	 * @param IOInterface|null $io An IO for writing to.
	 * @param string           $outDir The directory to place the autoloader files in.
	 * @param string           $suffix The suffix to use in the autoloader's namespace.
	 */
	public static function copyAutoloaderFiles( $io, $outDir, $suffix ) {
		$renameList = array(
			'autoload.php' => '../autoload_packages.php',
		);
		$ignoreList = array(
			'AutoloadGenerator.php',
			'AutoloadProcessor.php',
			'CustomAutoloaderPlugin.php',
			'ManifestGenerator.php',
			'AutoloadFileWriter.php',
		);

		// Copy all of the autoloader files.
		$files = scandir( __DIR__ );
		foreach ( $files as $file ) {
			// Only PHP files will be copied.
			if ( substr( $file, -4 ) !== '.php' ) {
				continue;
			}

			if ( in_array( $file, $ignoreList, true ) ) {
				continue;
			}

			$newFile = $renameList[ $file ] ?? $file;
			$content = self::prepareAutoloaderFile( $file, $suffix );

			$written = file_put_contents( $outDir . '/' . $newFile, $content );
			if ( $io ) {
				if ( $written ) {
					$io->writeError( "  <info>Generated: $newFile</info>" );
				} else {
					$io->writeError( "  <error>Error: $newFile</error>" );
				}
			}
		}
	}

	/**
	 * Prepares an autoloader file to be written to the destination.
	 *
	 * @param String $filename a file to prepare.
	 * @param String $suffix   Unique suffix used in the namespace.
	 *
	 * @return string
	 */
	private static function prepareAutoloaderFile( $filename, $suffix ) {
		$header  = self::COMMENT;
		$header .= PHP_EOL;
		if ( $suffix === 'Current' ) {
			// Unit testing.
			$header .= 'namespace Automattic\Jetpack\Autoloader\jpCurrent;';
		} else {
			$header .= 'namespace Automattic\Jetpack\Autoloader\jp' . $suffix . '\al' . preg_replace( '/[^0-9a-zA-Z]/', '_', AutoloadGenerator::VERSION ) . ';';
		}
		$header .= PHP_EOL . PHP_EOL;

		$sourceLoader  = fopen( __DIR__ . '/' . $filename, 'r' );
		$file_contents = stream_get_contents( $sourceLoader );
		return str_replace(
			'/* HEADER */',
			$header,
			$file_contents
		);
	}
}
