<?php
/* ============================================================================
 * Copyright 2020 Zindex Software
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * ============================================================================ */

namespace Opis\JsonSchema\Parsers;

use Opis\JsonSchema\Variables;
use Opis\JsonSchema\Variables\{VariablesContainer};

trait VariablesTrait
{
    /**
     * @param SchemaParser $parser
     * @param array|object $vars
     * @param bool $lazy
     * @return Variables
     */
    protected function createVariables(SchemaParser $parser, $vars, bool $lazy = true): Variables
    {
        return new VariablesContainer(
            $vars,
            $lazy,
            $parser->option('varRefKey', '$ref'),
            $parser->option('varEachKey', '$each'),
            $parser->option('varDefaultKey', 'default')
        );
    }
}